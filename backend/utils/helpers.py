#!/usr/bin/env python3
"""
工具函数模块
包含各种辅助函数和工具方法
"""

import torch
import torch.serialization


def setup_torch_compatibility():
    """设置PyTorch兼容性"""
    # 全局修复PyTorch 2.6+与YOLO模型的兼容性 - 必须在最前面
    _original_torch_load = torch.load
    
    def _patched_torch_load(*args, **kwargs):
        if 'weights_only' not in kwargs:
            kwargs['weights_only'] = False
        return _original_torch_load(*args, **kwargs)
    
    torch.load = _patched_torch_load

    # 添加安全全局变量
    try:
        from ultralytics.nn.tasks import SegmentationModel
        torch.serialization.add_safe_globals([SegmentationModel])
    except ImportError:
        pass


def cleanup_old_files(directory, pattern, current_file=None):
    """清理旧文件"""
    import os
    from pathlib import Path
    
    directory = Path(directory)
    for old_file in directory.glob(pattern):
        if current_file and old_file == current_file:
            continue
        if os.path.exists(old_file):
            try:
                os.remove(old_file)
                print(f"删除旧文件: {old_file}")
            except Exception as e:
                print(f"删除文件失败 {old_file}: {e}")


def ensure_directory_exists(directory):
    """确保目录存在"""
    from pathlib import Path
    
    directory = Path(directory)
    directory.mkdir(parents=True, exist_ok=True)
    return directory


def format_similarity_score(score, as_percentage=True):
    """格式化相似度分数"""
    if as_percentage:
        return f"{score * 100:.1f}%"
    else:
        return f"{score:.3f}"


def safe_filename(filename):
    """生成安全的文件名"""
    import re
    
    # 移除或替换不安全的字符
    filename = re.sub(r'[^\w\-_\.]', '_', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
    
    return filename


def get_image_dimensions(image_path):
    """获取图像尺寸"""
    import cv2
    
    try:
        img = cv2.imread(str(image_path))
        if img is not None:
            return img.shape[:2]  # height, width
    except Exception as e:
        print(f"获取图像尺寸失败: {e}")
    
    return None, None


def resize_image_maintain_aspect(image, max_size):
    """调整图像大小并保持长宽比"""
    import cv2
    
    h, w = image.shape[:2]
    if max(h, w) <= max_size:
        return image
    
    if w > h:
        new_w = max_size
        new_h = int(h * max_size / w)
    else:
        new_h = max_size
        new_w = int(w * max_size / h)
    
    return cv2.resize(image, (new_w, new_h))


def validate_image_file(file_path):
    """验证图像文件是否有效"""
    import cv2
    from pathlib import Path
    
    file_path = Path(file_path)
    
    # 检查文件是否存在
    if not file_path.exists():
        return False, "文件不存在"
    
    # 检查文件扩展名
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    if file_path.suffix.lower() not in valid_extensions:
        return False, "不支持的图像格式"
    
    # 尝试读取图像
    try:
        img = cv2.imread(str(file_path))
        if img is None:
            return False, "无法读取图像文件"
        
        h, w = img.shape[:2]
        if h == 0 or w == 0:
            return False, "图像尺寸无效"
        
        return True, "有效图像"
    
    except Exception as e:
        return False, f"图像验证失败: {str(e)}"


def create_error_image(width=400, height=300, message="Image Not Found"):
    """创建错误提示图像"""
    import cv2
    import numpy as np
    
    # 创建白色背景
    img = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 添加错误文本
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    color = (0, 0, 255)  # 红色
    thickness = 2
    
    # 计算文本尺寸并居中
    text_size = cv2.getTextSize(message, font, font_scale, thickness)[0]
    text_x = (width - text_size[0]) // 2
    text_y = (height + text_size[1]) // 2
    
    cv2.putText(img, message, (text_x, text_y), font, font_scale, color, thickness)
    
    return img


def encode_image_to_base64(image_path):
    """将图像编码为Base64"""
    import base64
    
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    except Exception as e:
        print(f"图像Base64编码失败: {e}")
        return None


def decode_base64_to_image(base64_string, output_path):
    """将Base64解码为图像"""
    import base64
    
    try:
        # 移除数据URL前缀（如果存在）
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码
        image_data = base64.b64decode(base64_string)
        
        # 保存图像
        with open(output_path, 'wb') as f:
            f.write(image_data)
        
        return True
    except Exception as e:
        print(f"Base64图像解码失败: {e}")
        return False


def generate_unique_id(prefix=""):
    """生成唯一ID"""
    import uuid
    import time
    
    timestamp = int(time.time())
    unique_id = str(uuid.uuid4()).replace('-', '')[:8]
    
    if prefix:
        return f"{prefix}_{timestamp}_{unique_id}"
    else:
        return f"{timestamp}_{unique_id}"


def log_system_info():
    """记录系统信息"""
    import platform
    import sys
    import torch
    
    print("=== 系统信息 ===")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    
    try:
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
    except ImportError:
        print("OpenCV: 未安装")
    
    try:
        from ultralytics import __version__ as yolo_version
        print(f"Ultralytics版本: {yolo_version}")
    except ImportError:
        print("Ultralytics: 未安装")
    
    print("================")


def measure_execution_time(func):
    """装饰器：测量函数执行时间"""
    import time
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"{func.__name__} 执行时间: {execution_time:.3f} 秒")
        return result
    
    return wrapper


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, name="Timer"):
        self.name = name
        self.start_time = None
        
    def __enter__(self):
        import time
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        if self.start_time:
            elapsed = time.time() - self.start_time
            print(f"{self.name}: {elapsed:.3f} 秒")


def memory_usage():
    """获取内存使用情况"""
    import psutil
    import os
    
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }
    except ImportError:
        return {"error": "psutil not available"}


def print_memory_usage(label=""):
    """打印内存使用情况"""
    usage = memory_usage()
    if "error" not in usage:
        print(f"内存使用 {label}: RSS={usage['rss']:.1f}MB, "
              f"VMS={usage['vms']:.1f}MB, "
              f"百分比={usage['percent']:.1f}%")
    else:
        print(f"无法获取内存使用信息: {usage['error']}")


def is_port_available(port):
    """检查端口是否可用"""
    import socket
    
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except socket.error:
            return False


def find_available_port(start_port=8080, max_attempts=10):
    """查找可用端口"""
    for i in range(max_attempts):
        port = start_port + i
        if is_port_available(port):
            return port
    return None 