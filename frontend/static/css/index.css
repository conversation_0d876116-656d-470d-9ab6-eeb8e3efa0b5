/* 主页样式文件 */

.main-container {
    display: flex;
    flex: 1;
    width: 100%;
    height: 100vh;
}

.left-panel {
    flex: 1;
    position: relative;
    background-color: #f0f0f0;
    overflow: hidden;
}

.slideshow {
    width: 100%;
    height: 100%;
    position: relative;
}

.slideshow img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slideshow img.active {
    opacity: 1;
}

.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.camera-overlay {
    position: relative;
    flex: 1;
    overflow: hidden;
}

.preview-label {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 5;
}

.info-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20px 30px;
    border-radius: 8px;
    text-align: center;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.info-overlay h2 {
    margin-bottom: 20px;
    font-size: 28px;
    font-weight: bold;
}

.step-item {
    display: flex;
    align-items: center;
    margin: 15px 0;
    text-align: left;
}

.step-circle {
    width: 30px;
    height: 30px;
    background-color: white;
    color: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-text {
    flex: 1;
}

.step-text h3 {
    margin-bottom: 5px;
    font-size: 18px;
}

.step-text p {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
}

.start-button {
    margin-top: 30px;
    background-color: white;
    color: #333;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    border: none;
    font-size: 16px;
    transition: all 0.2s;
}

.start-button:hover {
    background-color: #f0f0f0;
    transform: translateY(-2px);
}

.start-button span {
    margin-right: 8px;
}

/* 摄像头覆盖层样式 */
#overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-container {
        flex-direction: column;
    }
    
    .left-panel,
    .right-panel {
        flex: 1;
        min-height: 50vh;
    }
    
    .info-overlay {
        padding: 15px 20px;
    }
    
    .info-overlay h2 {
        font-size: 24px;
    }
    
    .step-text h3 {
        font-size: 16px;
    }
    
    .step-text p {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .step-item {
        flex-direction: column;
        text-align: center;
        margin: 10px 0;
    }
    
    .step-circle {
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .start-button {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .preview-label {
        font-size: 12px;
        padding: 4px 8px;
    }
} 