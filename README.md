# 动物姿态匹配器 (Animal Pose Matcher)

一个基于深度学习的动物姿态匹配系统，可以分析人体姿势并找到最相似的动物姿态。

## 项目结构

经过重构后，项目采用了清晰的前后端分离架构：

```
pose_matching_project/
├── main.py                     # 主启动文件
├── requirements.txt            # 依赖包列表
├── README.md                   # 项目说明
├── backend/                    # 后端代码
│   ├── models/                 # 核心算法模块
│   │   ├── contour_matcher.py  # 轮廓匹配算法
│   │   └── image_processor.py  # 图像处理模块
│   ├── api/                    # API接口模块
│   │   ├── routes.py          # 路由处理器
│   │   └── animal_info.py     # 动物信息API
│   ├── database/              # 数据库管理
│   │   └── db_manager.py      # 数据库管理器
│   ├── utils/                 # 工具函数
│   │   ├── helpers.py         # 辅助函数
│   │   └── template_manager.py # 模板管理器
│   └── retrieval_system.py   # 主检索系统
├── frontend/                  # 前端代码
│   ├── templates/             # HTML模板
│   │   ├── index.html         # 主页模板
│   │   ├── take-photo.html    # 拍照页面
│   │   └── result.html        # 结果页面
│   └── static/                # 静态资源
│       ├── css/               # 样式文件
│       │   ├── main.css       # 主样式
│       │   ├── index.css      # 主页样式
│       │   ├── take-photo.css # 拍照页面样式
│       │   └── result.css     # 结果页面样式
│       └── js/                # JavaScript文件
│           ├── main.js        # 主要脚本
│           ├── index.js       # 主页脚本
│           ├── take-photo.js  # 拍照功能脚本
│           └── result.js      # 结果页面脚本
├── models/                    # AI模型文件
│   └── human_seg_model.pt     # 人体分割模型
├── database/                  # 数据库文件
│   └── animal_contours.pkl    # 动物轮廓数据库
└── results/                   # 结果输出目录
```

## 核心功能

### 后端模块

1. **轮廓匹配器 (ContourMatcher)**
   - 负责轮廓特征提取和相似度计算
   - 使用KD-Tree进行快速检索
   - 计算Hu矩、圆形度、长宽比等形状特征

2. **图像处理器 (ImageProcessor)**
   - 人体轮廓提取（基于YOLO分割模型）
   - 图像预处理和后处理
   - 动物检测和掩码生成

3. **路由处理器 (RouteHandler)**
   - 处理HTTP请求路由
   - 管理API接口和静态文件服务
   - 实时轮廓检测API

4. **动物信息API (AnimalInfoAPI)**
   - 集成Gemini AI获取动物详细信息
   - 提供备用动物数据
   - 支持多语言动物信息

5. **数据库管理器 (DatabaseManager)**
   - 管理动物图像和标注数据
   - 提供图像-标注对的查询功能
   - 处理博物馆位置信息

### 前端界面

1. **主页 (index.html)**
   - 博物馆图像幻灯片展示
   - 实时摄像头预览
   - 人体轮廓实时检测
   - 交互式使用指南

2. **拍照页面 (take-photo.html)**
   - 全屏摄像头界面
   - 实时轮廓覆盖显示
   - 拍照倒计时功能
   - 直观的用户控制

3. **结果页面 (result.html)**
   - 分屏对比显示
   - 相似度分数展示
   - 动物详细信息模态框
   - 博物馆位置查询

## 安装和运行

### 环境要求

- Python 3.8+
- OpenCV
- PyTorch
- Ultralytics YOLO
- NumPy, SciPy, Matplotlib
- Google GenerativeAI

### 安装步骤

1. 克隆或下载项目到本地
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 确保必要的数据文件存在：
   - `models/human_seg_model.pt` - 人体分割模型
   - `database/animal_contours.pkl` - 动物轮廓数据库
   - `/Users/<USER>/Desktop/museum/` - 动物图像
   - `/Users/<USER>/Desktop/label museum/` - 图像标注

### 运行方式

#### Web模式（推荐）
```bash
python main.py --mode web --port 8080
```
然后在浏览器中访问 `http://localhost:8080`

#### 摄像头模式
```bash
python main.py --mode camera
```
使用OpenCV窗口进行拍照和匹配

### 使用说明

1. **启动系统**：运行主程序，系统会自动打开浏览器
2. **摆姿势**：在摄像头前模仿动物的特征姿势
3. **拍照匹配**：点击拍照按钮，系统会分析您的姿势
4. **查看结果**：查看匹配的动物和相似度分数
5. **获取信息**：点击动物信息按钮了解更多详情

## 技术特点

### 模块化设计
- 前后端完全分离
- 单一职责原则
- 易于维护和扩展

### 高精度匹配
- 基于深度学习的人体分割
- 多维度形状特征提取
- KD-Tree快速相似度检索

### 用户友好界面
- 响应式设计
- 实时视觉反馈
- 直观的操作流程

### AI集成
- Gemini AI动物信息获取
- YOLO模型人体检测
- 智能图像处理流水线

## 开发说明

### 代码重构原则
1. **只移动，不修改**：保持业务逻辑不变
2. **前后端分离**：UI代码移至frontend，服务器逻辑移至backend
3. **功能模块化**：每个文件只负责一个具体功能
4. **100%功能稳定**：重构后系统功能完全保持不变

### 扩展建议
- 添加更多动物类别
- 支持多人姿势检测
- 增加姿势评分细节
- 集成更多AI模型

## 故障排除

1. **摄像头访问失败**：检查摄像头权限和连接
2. **模型加载错误**：确认模型文件路径和PyTorch版本
3. **端口占用**：系统会自动寻找可用端口
4. **图像处理慢**：考虑使用GPU加速

## 许可证

本项目用于研究和教育目的。请参考相关AI模型的许可证要求。 