/* 主要样式文件 - 通用样式和基础布局 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-color: #000;
    color: white;
}

/* 按钮通用样式 */
.btn {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s;
    padding: 15px 30px;
}

.btn:hover {
    background-color: rgba(40, 40, 40, 0.7);
    transform: translateY(-2px);
}

/* 控制按钮样式 */
.control-btn {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: background-color 0.2s;
}

.control-btn:hover {
    background-color: rgba(40, 40, 40, 0.7);
}

/* 摄像头相关样式 */
.camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#overlayCanvas {
    /* 与视频同向（不镜像） */
}

canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

/* 模态框通用样式 */
.modal {
    display: none;
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    max-width: 700px;
    width: 100%;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    color: #333;
    overflow-y: auto;
    max-height: 90vh;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 32px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #000;
}

.status-tag {
    background-color: #D97706;
    color: white;
    padding: 4px 12px;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: normal;
}

.close-btn {
    background: none;
    border: none;
    color: #888;
    font-size: 32px;
    cursor: pointer;
}

.modal-body {
    color: #333;
    line-height: 1.6;
}

.section-title {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 20px 0;
}

.fun-fact {
    background-color: #f3f4f6;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.fun-fact-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.location-card {
    background-color: #f3f4f6;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.location-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-weight: 600;
}

.map-placeholder {
    background-color: #ddd;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: #888;
    margin: 20px 0;
}

.close-button {
    width: 100%;
    background-color: #444;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 20px;
    font-size: 16px;
    transition: background-color 0.2s;
}

.close-button:hover {
    background-color: #333;
}

/* 图像样式 */
.result-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .btn {
        font-size: 16px;
        padding: 12px 24px;
    }
    
    .modal-content {
        margin: 10px;
        padding: 15px;
    }
    
    .modal-title {
        font-size: 24px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner {
    border: 8px solid #f3f3f3;
    border-top: 8px solid #111;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 2s linear infinite;
    margin: 0 auto;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 透明度过渡 */
.fade-in {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.fade-in.visible {
    opacity: 1;
} 