<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Animal Pose Matcher</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    font-family: 'Helvetica Neue', Arial, sans-serif;
                }
                
                body {
                    width: 100vw;
                    height: 100vh;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    background-color: #f5f5f5;
                }
                
                .header {
                    width: 100%;
                    padding: 1rem;
                    background-color: #333;
                    color: white;
                    text-align: center;
                    font-size: 1.5rem;
                }
                
                .content-wrapper {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: flex-start;
                    width: 100%;
                    overflow-y: auto;
                    padding: 1rem;
                }
                
                .step-container {
                    width: 100%;
                    max-width: 800px;
                    margin-bottom: 1rem;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                }
                
                .step-header {
                    background-color: #4CAF50;
                    color: white;
                    padding: 0.8rem 1rem;
                    font-size: 1.2rem;
                    font-weight: bold;
                }
                
                .step-content {
                    padding: 1rem;
                }
                
                .camera-container {
                    position: relative;
                    width: 100%;
                    aspect-ratio: 4/3;
                    overflow: hidden;
                    background-color: #000;
                    border-radius: 4px;
                }
                
                #video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                
                #canvas {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10;
                    object-fit: cover;
                }
                
                #outline-canvas {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 15;
                    object-fit: cover;
                }
                
                .countdown {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 6rem;
                    color: white;
                    z-index: 20;
                    text-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
                    opacity: 0;
                    transition: opacity 0.3s;
                }
                
                .button-container {
                    display: flex;
                    justify-content: center;
                    margin-top: 1rem;
                    gap: 1rem;
                }
                
                button {
                    padding: 0.8rem 1.5rem;
                    border: none;
                    border-radius: 4px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: background-color 0.3s;
                }
                
                .btn-primary {
                    background-color: #4CAF50;
                    color: white;
                }
                
                .btn-primary:hover {
                    background-color: #45a049;
                }
                
                .btn-secondary {
                    background-color: #f1f1f1;
                    color: #333;
                }
                
                .btn-secondary:hover {
                    background-color: #e0e0e0;
                }
                
                .result-container {
                    display: none;
                    width: 100%;
                }

                /* Full-screen result styling */
                .result-screen {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: #f5f5f5;
                    z-index: 100;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }
                
                .result-header {
                    width: 100%;
                    padding: 1rem;
                    background-color: #333;
                    color: white;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .result-header h2 {
                    font-size: 1.5rem;
                    margin: 0;
                }
                
                .result-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 1rem;
                    overflow-y: auto;
                }
                
                .result-footer {
                    width: 100%;
                    padding: 1rem;
                    background-color: #f1f1f1;
                    display: flex;
                    justify-content: space-around;
                    border-top: 1px solid #ddd;
                }
                
                .result-card {
                    width: 100%;
                    max-width: 800px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                    margin-bottom: 1rem;
                    overflow: hidden;
                }
                
                .result-card-header {
                    background-color: #4CAF50;
                    color: white;
                    padding: 0.8rem 1rem;
                    font-size: 1.2rem;
                    font-weight: bold;
                }
                
                .result-card-content {
                    padding: 1rem;
                }
                
                .result-images {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-around;
                    gap: 1rem;
                }
                
                .result-image-container {
                    flex: 1;
                    min-width: 250px;
                    max-width: 350px;
                    margin-bottom: 1rem;
                }
                
                .result-image-container img {
                    width: 100%;
                    height: auto;
                    border-radius: 4px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                }
                
                .result-image-title {
                    text-align: center;
                    margin-top: 0.5rem;
                    font-weight: bold;
                }
                
                .similarity-section {
                    text-align: center;
                    margin: 1.5rem 0;
                }
                
                .similarity-score {
                    font-size: 2.5rem;
                    font-weight: bold;
                    color: #4CAF50;
                }
                
                .similarity-label {
                    font-size: 1rem;
                    color: #666;
                }
                
                /* Modal styling */
                .modal {
                    display: none;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    z-index: 200;
                    align-items: center;
                    justify-content: center;
                }
                
                .modal-content {
                    background-color: white;
                    width: 90%;
                    max-width: 500px;
                    border-radius: 8px;
                    overflow: hidden;
                    max-height: 90vh;
                    display: flex;
                    flex-direction: column;
                }
                
                .modal-header {
                    padding: 1rem;
                    background-color: #4CAF50;
                    color: white;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .modal-title {
                    font-size: 1.2rem;
                    font-weight: bold;
                    margin: 0;
                }
                
                .modal-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0;
                    margin: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .modal-body {
                    padding: 1rem;
                    overflow-y: auto;
                }
                
                .modal-footer {
                    padding: 1rem;
                    border-top: 1px solid #ddd;
                    display: flex;
                    justify-content: flex-end;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Animal Pose Matcher</h1>
            </div>
        
            <div class="content-wrapper">
                <!-- Step 1: Capture Photo -->
                <div class="step-container" id="capture-container">
                    <div class="step-header">Step 1: Capture Your Photo</div>
                    <div class="step-content">
                        <div class="camera-container">
                            <video id="video" autoplay playsinline></video>
                            <canvas id="canvas"></canvas>
                            <canvas id="outline-canvas"></canvas>
                            <div class="countdown" id="countdown">3</div>
                        </div>
                        <div class="button-container">
                            <button class="btn-secondary" id="switch-camera">Switch Camera</button>
                            <button class="btn-primary" id="take-photo">Take Photo</button>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Loading (initially hidden) -->
                <div class="step-container" id="loading-container" style="display: none;">
                    <div class="step-header">Step 2: Processing Your Photo</div>
                    <div class="step-content">
                        <div style="text-align: center; padding: 2rem;">
                            <div style="margin-bottom: 1rem;">Analyzing your pose...</div>
                            <div style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #4CAF50; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Results (initially hidden) -->
                <div class="result-container" id="result-container"></div>
            </div>
            
            <!-- Full-screen Result View (initially hidden) -->
            <div class="result-screen" id="result-screen" style="display: none;"></div>
            
            <!-- Animal Info Modal -->
            <div class="modal" id="animal-info-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="animal-info-title">Animal Information</h3>
                        <button class="modal-close" id="close-animal-info">&times;</button>
                    </div>
                    <div class="modal-body" id="animal-info-content">
                        <!-- Animal information will be loaded here -->
                    </div>
                </div>
            </div>
            
            <!-- Museum Map Modal -->
            <div class="modal" id="map-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">Find in Museum</h3>
                        <button class="modal-close" id="close-map">&times;</button>
                    </div>
                    <div class="modal-body" id="map-content">
                        <!-- Map will be loaded here -->
                    </div>
                </div>
            </div>
            
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
            
            <script>
                // ... existing code ...
            </script>
        </body>
        </html>