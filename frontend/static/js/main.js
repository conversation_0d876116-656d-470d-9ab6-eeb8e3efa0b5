// 主要JavaScript文件 - 通用功能

// 全局变量
let camera = null;
// 避免与拍照页脚本冲突，使用独立命名
let overlayCanvasMain = null;
let overlayCtxMain = null;

// 初始化函数
function initCamera() {
    const video = document.getElementById('camera');
    if (!video) return;

    // 获取摄像头权限并显示视频流
    navigator.mediaDevices.getUserMedia({ 
        video: { 
            width: { ideal: 1280 }, 
            height: { ideal: 720 },
            facingMode: 'user'
        } 
    })
    .then(function(stream) {
        video.srcObject = stream;
        video.play();
        console.log('摄像头初始化成功');
        
        // 如果在拍照页面，等待视频加载完成后开始实时分割
        video.addEventListener('loadeddata', function() {
            if (typeof startRealtimeSegmentation === 'function') {
                setTimeout(startRealtimeSegmentation, 1000);
            }
        });
    })
    .catch(function(err) {
        console.error('无法访问摄像头:', err);
        // 显示错误消息
        const errorMsg = document.createElement('div');
        errorMsg.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            z-index: 100;
        `;
        errorMsg.innerHTML = `
            <h3>无法访问摄像头</h3>
            <p>请确保已授权摄像头权限</p>
            <small>${err.message}</small>
        `;
        document.body.appendChild(errorMsg);
    });
}

// 初始化覆盖层画布
function initOverlay() {
    overlayCanvasMain = document.getElementById('overlayCanvas');
    if (overlayCanvasMain) {
        overlayCtxMain = overlayCanvasMain.getContext('2d');
        
        // 设置画布尺寸
        function resizeCanvas() {
            const video = document.getElementById('camera');
            if (video) {
                overlayCanvasMain.width = video.offsetWidth;
                overlayCanvasMain.height = video.offsetHeight;
            }
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', resizeCanvas);
        
        // 初始设置
        setTimeout(resizeCanvas, 100);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化...');
    initCamera();
    initOverlay();
});

// 工具函数：显示加载状态
function showLoading(message = '加载中...') {
    const loading = document.createElement('div');
    loading.id = 'loading';
    loading.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-size: 18px;
    `;
    loading.innerHTML = `
        <div style="text-align: center;">
            <div style="margin-bottom: 20px;">⏳</div>
            <div>${message}</div>
        </div>
    `;
    document.body.appendChild(loading);
}

// 工具函数：隐藏加载状态
function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.remove();
    }
}

// 工具函数：显示错误消息
function showError(message) {
    const error = document.createElement('div');
    error.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 0, 0, 0.9);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 9999;
        max-width: 300px;
    `;
    error.textContent = message;
    document.body.appendChild(error);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (error.parentNode) {
            error.remove();
        }
    }, 3000);
} 