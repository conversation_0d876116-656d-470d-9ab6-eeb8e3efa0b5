/* 拍照页面样式文件 */

.camera-container {
    position: relative;
    width: 100vw;
    height: 100dvh; /* 使用动态视口高度以适应移动端浏览器 */
    overflow: hidden;
    background-color: #000;
}

.camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#captureCanvas {
    display: none;
}

#overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

/* 头部覆盖层 */
.header-overlay {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 15px 30px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.header-overlay h1 {
    font-size: 24px;
    margin-bottom: 8px;
    font-weight: bold;
}

.header-overlay p {
    font-size: 16px;
    opacity: 0.9;
}

/* 倒计时显示 */
.countdown {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 72px;
    font-weight: bold;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 20;
    animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

/* 倒计时文字 */
.countdown-text {
    position: absolute;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 10px 20px;
    border-radius: 20px;
    z-index: 15;
    display: none;
}

/* 底部控制按钮 */
.footer-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 10;
}

.btn-back {
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-back:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.7);
}

.btn-capture {
    background-color: rgba(255, 59, 48, 0.8);
    border: 2px solid rgba(255, 59, 48, 1);
    min-width: 120px;
}

.btn-capture:hover {
    background-color: rgba(255, 59, 48, 0.9);
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-overlay {
        top: 10px;
        padding: 10px 20px;
    }
    
    .header-overlay h1 {
        font-size: 20px;
    }
    
    .header-overlay p {
        font-size: 14px;
    }
    
    .countdown {
        width: 120px;
        height: 120px;
        font-size: 60px;
    }
    
    .footer-controls {
        bottom: 20px;
        gap: 15px;
    }
    
    .btn {
        padding: 12px 24px;
        font-size: 16px;
    }
} 