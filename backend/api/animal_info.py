#!/usr/bin/env python3
"""
动物信息API模块
负责从Gemini API获取动物的详细信息
"""

import json
import google.generativeai as genai


class AnimalInfoAPI:
    """动物信息API处理器"""
    
    def __init__(self, api_key=None):
        """初始化API处理器"""
        self.api_key = api_key or "AIzaSyA8A08MuhkHq4oyWqj0TXcAUnxNz-xwzaE"
        
        # 配置Gemini API
        try:
            genai.configure(api_key=self.api_key)
            print("Gemini API初始化成功")
        except Exception as e:
            print(f"Gemini API初始化失败: {e}")

    def get_animal_info(self, animal_name):
        """使用Gemini API获取动物的详细信息"""
        try:
            # 配置Gemini模型
            generation_config = {
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 2048,
            }

            # 创建Gemini模型实例
            model = genai.GenerativeModel(
                model_name="gemini-2.0-flash-exp",
                generation_config=generation_config
            )

            # 构建提示，要求以JSON格式返回特定结构的数据
            prompt = f"""
            You are a strict JSON data generation service. Your sole task is to provide information about the animal "{animal_name}".
            IMPORTANT: Use ONLY information from Wikipedia. All facts must be accurate and verifiable from Wikipedia sources.
            Your response MUST be a single, perfectly formatted JSON object and nothing else. Do not include any explanatory text, markdown formatting, or any characters before the opening brace `{{` or after the closing brace `}}`.

            The JSON object must contain the following keys:
            - "conservation_status": A string, e.g., "Vulnerable", "Endangered", "Least Concern".
            - "description": A string containing a brief, engaging paragraph suitable for a museum display.
            - "diet": A string describing its primary diet.
            - "habitat": A string describing its natural habitat.
            - "lifespan": A string indicating a typical lifespan range.
            - "weight": A string indicating a typical weight range.
            - "fun_fact": A string with a single, interesting fun fact.

            Here is a mandatory format example to follow:
            {{
              "conservation_status": "Vulnerable",
              "description": "Known as the 'king of the jungle', the lion is a large cat of the genus Panthera. It has a muscular, deep-chested body and a short, rounded head. The male is distinguished by his impressive mane.",
              "diet": "Carnivore, primarily hunting large mammals.",
              "habitat": "Grasslands, savannas, and open woodlands of Africa and India.",
              "lifespan": "10-14 years in the wild.",
              "weight": "150-250 kg (male)",
              "fun_fact": "A lion's roar can be heard from up to 8 kilometers (5 miles) away."
            }}
            """

            # 发送请求到Gemini API
            response = model.generate_content(prompt)
            
            if not response or not response.text:
                raise Exception("从Gemini API收到空响应")

            # 解析响应文本
            response_text = response.text

            try:
                # 尝试直接解析JSON
                animal_info = json.loads(response_text)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取JSON部分
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1

                if start_idx != -1 and end_idx != -1:
                    json_str = response_text[start_idx:end_idx]
                    try:
                        animal_info = json.loads(json_str)
                    except json.JSONDecodeError:
                        raise
                else:
                    raise Exception("响应中未找到有效的JSON")

            # 验证并补充缺失的字段
            default_info = self._get_default_animal_info()

            # 确保所有必要的字段都存在
            for key in default_info:
                if key not in animal_info or not animal_info[key]:
                    animal_info[key] = default_info[key]

            return {
                "success": True,
                "data": animal_info
            }

        except Exception as e:
            print(f"获取动物信息失败: {e}")
            # 返回默认信息
            return {
                "success": True,
                "data": self._get_default_animal_info(),
                "error_message": str(e)
            }

    def _get_default_animal_info(self):
        """获取默认的动物信息"""
        return {
            "conservation_status": "数据不足",
            "description": "这种动物是我们博物馆收藏的一部分。请参观展览了解更多关于它的栖息地、行为和保护状态的信息。",
            "diet": "因物种而异",
            "habitat": "分布在各个地区的自然栖息地",
            "lifespan": "因物种而异",
            "weight": "因物种和年龄而异",
            "fun_fact": "这种动物是我们博物馆重要保护项目的一部分。"
        }

    def get_fallback_info(self, animal_name):
        """获取预定义的备用动物信息"""
        fallback_data = {
            'giraffe': {
                'conservation_status': 'Vulnerable',
                'description': '长颈鹿是现存最高的陆地动物。其独特特征是极长的脖子和腿、角状的骨质突起以及斑点状的皮毛图案。长颈鹿具有专门的心血管系统来管理大脑和心脏之间的血压。',
                'diet': '草食性动物 - 主要食用相思树叶和嫩芽',
                'habitat': '稀树草原、草地和开阔林地',
                'lifespan': '野生环境中20-25年',
                'weight': '800-1,930公斤',
                'fun_fact': '长颈鹿在24小时内只需要5-30分钟的睡眠，通常分成很短的小憩。'
            },
            'elephant': {
                'conservation_status': 'Endangered',
                'description': '大象是现存最大的陆地动物，以其智慧、长鼻和象牙而闻名。它们具有复杂的社会结构，是具有出色记忆力的高智商动物。',
                'diet': '草食性动物 - 每日食用高达300公斤植物',
                'habitat': '森林、沙漠、沼泽和稀树草原',
                'lifespan': '60-70年',
                'weight': '2,700-6,000公斤',
                'fun_fact': '大象可以在镜子中认出自己，显示出少数动物才具有的自我意识。'
            },
            'lion': {
                'conservation_status': 'Vulnerable',
                'description': '狮子被称为"丛林之王"，是猫科动物中的大型猫科动物。它具有肌肉发达、胸部深厚的身体和短而圆的头部。雄狮的特征是令人印象深刻的鬃毛。',
                'diet': '肉食性动物 - 主要捕食大型哺乳动物',
                'habitat': '非洲和印度的草地、稀树草原和开阔林地',
                'lifespan': '野生环境中10-14年',
                'weight': '150-250公斤（雄性）',
                'fun_fact': '狮子的吼声可以在8公里（5英里）外听到。'
            },
            'zebra': {
                'conservation_status': 'Near Threatened',
                'description': '斑马是具有独特黑白条纹皮毛的非洲马科动物。每只斑马都有自己独特的条纹图案，就像人类的指纹一样独特。',
                'diet': '草食性动物 - 主要食用草类',
                'habitat': '平原、草地和轻林地',
                'lifespan': '野生环境中25-30年',
                'weight': '350-450公斤',
                'fun_fact': '每只斑马都有独特的条纹图案，就像人类的指纹。'
            },
            'rhino': {
                'conservation_status': 'Critically Endangered',
                'description': '犀牛是以其特征性角状突起鼻部而识别的大型草食性哺乳动物。它们厚实的保护性皮肤由胶原蛋白层形成。',
                'diet': '草食性动物 - 草类、叶子、水果',
                'habitat': '草地和洪泛平原',
                'lifespan': '35-50年',
                'weight': '1,800-2,700公斤',
                'fun_fact': '犀牛的视力很差，但听觉和嗅觉非常敏锐。'
            }
        }
        
        animal_key = animal_name.lower()
        if animal_key in fallback_data:
            return {
                "success": True,
                "data": fallback_data[animal_key]
            }
        else:
            return {
                "success": True,
                "data": self._get_default_animal_info()
            } 