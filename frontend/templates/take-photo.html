<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照 - 动物姿态匹配器</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/take-photo.css">
</head>
<body>
    <div class="camera-container">
        <video id="camera" class="camera-feed" autoplay></video>
        <canvas id="captureCanvas" style="display:none;"></canvas>
        <canvas id="overlayCanvas"></canvas>

        <div class="header-overlay">
            <h1>摆出您最棒的动物姿势！</h1>
            <p>尝试模仿动物的特征姿势</p>
        </div>

        <div class="countdown" id="countdown">3</div>

        <div class="footer-controls">
            <button class="btn btn-back" id="backBtn">返回</button>
            <button class="btn btn-capture" id="captureBtn">拍照</button>
        </div>
        <div class="countdown-text" id="countdownText">正在拍照...</div>
    </div>

    <script src="/static/js/main.js"></script>
    <script src="/static/js/take-photo.js"></script>
</body>
</html> 