#!/usr/bin/env python3
"""
模板管理器
负责HTML模板的加载和渲染
"""

from pathlib import Path


class TemplateManager:
    """HTML模板管理器"""
    
    def __init__(self, template_dir):
        """初始化模板管理器"""
        self.template_dir = Path(template_dir)
        
    def get_template(self, template_name):
        """获取模板内容"""
        template_path = self.template_dir / template_name
        
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件未找到: {template_path}")
            
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"读取模板文件失败: {e}")

    def get_result_template(self, results, similarity_score):
        """获取结果页面模板"""
        if not results or len(results) == 0:
            return self.get_template('error.html')
            
        result = results[0]
        animal_category = result['category'].capitalize()
        similarity_percentage = int(similarity_score * 100)
        
        # 读取结果模板并替换变量
        template_content = self.get_template('result.html')
        
        # 简单的模板变量替换
        template_content = template_content.replace('{{animal_category}}', animal_category)
        template_content = template_content.replace('{{similarity_score}}', str(similarity_percentage))
        
        return template_content

    def render_template(self, template_name, **context):
        """渲染模板（简单变量替换）"""
        template_content = self.get_template(template_name)
        
        for key, value in context.items():
            placeholder = f'{{{{{key}}}}}'
            template_content = template_content.replace(placeholder, str(value))
            
        return template_content 