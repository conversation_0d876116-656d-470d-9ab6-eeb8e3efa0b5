#!/usr/bin/env python3
"""
主检索系统
集成所有后端模块，提供完整的检索服务
"""

import os
import json
import random
import hashlib
from pathlib import Path
import matplotlib.pyplot as plt
import cv2
import numpy as np

# 导入我们的模块
from .models.contour_matcher import ContourMatcher  
from .models.image_processor import ImageProcessor
from .database.db_manager import DatabaseManager
from .utils.helpers import ensure_directory_exists, cleanup_old_files


class RetrievalSystem:
    """主检索系统"""
    
    def __init__(self, base_dir=None, results_dir=None, human_model_path=None, animal_db_path=None):
        """初始化检索系统"""
        # 设置路径
        if base_dir is None:
            self.base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
        else:
            self.base_dir = Path(base_dir)

        if results_dir is None:
            self.results_dir = self.base_dir / 'results'
        else:
            self.results_dir = Path(results_dir)

        if human_model_path is None:
            self.human_model_path = self.base_dir / 'models' / 'human_seg_model.pt'
        else:
            self.human_model_path = Path(human_model_path)

        if animal_db_path is None:
            self.animal_db_path = self.base_dir / 'database' / 'animal_contours.pkl'
        else:
            self.animal_db_path = Path(animal_db_path)

        # 确保结果目录存在
        ensure_directory_exists(self.results_dir)

        # 初始化组件
        print("初始化检索系统组件...")
        
        self.contour_matcher = ContourMatcher(self.animal_db_path)
        print("轮廓匹配器初始化完成")
        
        self.image_processor = ImageProcessor(self.human_model_path)  
        print("图像处理器初始化完成")
        
        self.db_manager = DatabaseManager()
        print("数据库管理器初始化完成")
        
        print("检索系统初始化完成")

    def extract_human_contour(self, image_path):
        """提取人体轮廓"""
        return self.image_processor.extract_human_contour(image_path)

    def retrieve(self, human_contour, top_k=1):
        """检索相似动物轮廓"""
        return self.contour_matcher.find_matches(human_contour, top_k)

    def retrieve_similar_poses(self, image_path, top_k=1):
        """完整的检索流程"""
        try:
            # 提取人体轮廓
            human_contour = self.extract_human_contour(image_path)
            
            if human_contour is None:
                print("未检测到人体轮廓")
                return []

            # 检索相似轮廓
            results = self.retrieve(human_contour, top_k=top_k)
            
            # 可视化结果
            if results:
                html_path = self.visualize_results(image_path, human_contour, results)
                print(f"结果已保存到: {html_path}")

            return results

        except Exception as e:
            print(f"检索过程中出错: {e}")
            return []

    def visualize_results(self, image_path, human_contour, results):
        """可视化检索结果"""
        if not results:
            return None

        # 创建matplotlib可视化
        self._create_matplotlib_visualization(image_path, human_contour, results)
        
        # 创建HTML结果页面
        html_path = self._create_html_result(image_path, human_contour, results)
        
        return html_path

    def _create_matplotlib_visualization(self, image_path, human_contour, results):
        """创建matplotlib可视化"""
        n_results = len(results)
        
        if n_results == 1:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            fig.suptitle('动物姿态匹配结果', fontsize=16, y=0.98)
        else:
            fig, axes = plt.subplots(1, n_results + 1, figsize=(4*(n_results + 1), 5))

        fig.patch.set_facecolor('white')

        # 显示人体输入图像
        self._display_human_image(axes, image_path, human_contour, n_results)
        
        # 显示检索结果
        self._display_animal_results(axes, results, n_results)

        # 保存可视化
        image_name = Path(image_path).stem
        plt.tight_layout()
        plt.savefig(str(self.results_dir / f"retrieval_results_{image_name}.png"), dpi=150)
        plt.close()

    def _display_human_image(self, axes, image_path, human_contour, n_results):
        """显示人体图像"""
        query_img = cv2.imread(str(image_path))
        query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)

        # 裁剪为1:1
        cropped_img = self.image_processor.crop_to_square(query_img)
        
        # 创建轮廓覆盖
        overlay_img = self._create_contour_overlay(cropped_img, human_contour)

        # 选择正确的轴
        ax_human = axes[0] if n_results == 1 else axes[0]
        ax_human.imshow(overlay_img)
        ax_human.axis('off')
        ax_human.set_title('您的姿势', fontsize=14, pad=10)

    def _display_animal_results(self, axes, results, n_results):
        """显示动物结果"""
        for i, result in enumerate(results):
            try:
                # 获取动物图像信息
                img_path, json_data = self.db_manager.get_animal_image_info(
                    result.get('category'), 
                    result.get('image_id')
                )
                
                if not img_path:
                    continue

                animal_img = cv2.imread(str(img_path))
                if animal_img is None:
                    continue

                animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)
                
                # 创建正方形图像
                square_img = self._create_square_image(animal_img)
                
                # 添加轮廓覆盖
                animal_overlay = self._add_animal_contour(square_img, result, json_data)

                # 显示结果
                ax_animal = axes[1] if n_results == 1 else axes[i+1]
                ax_animal.imshow(animal_overlay)
                ax_animal.axis('off')

                # 设置标题
                if n_results == 1:
                    title = f"{result['category']}\n{result['similarity_score']:.0f}% 匹配"
                    ax_animal.set_title(title, fontsize=14, pad=10)
                else:
                    ax_animal.set_title(f"{result['category']}\n{result['similarity_score']:.2f}",
                                      fontsize=12, pad=10)

            except Exception as e:
                print(f"显示结果 {i+1} 时出错: {e}")

    def _create_contour_overlay(self, image, contour):
        """创建轮廓覆盖图像"""
        overlay_img = image.copy()
        img_h, img_w = image.shape[:2]

        # 调整轮廓尺寸并居中
        resize_factor = min(img_h, img_w) / 100.0
        resized_contour = (contour * resize_factor).astype(np.int32)

        # 居中
        x, y, w, h = cv2.boundingRect(resized_contour)
        center_offset_x = (img_w - w) // 2 - x
        center_offset_y = (img_h - h) // 2 - y
        centered_contour = resized_contour + [center_offset_x, center_offset_y]

        # 绘制轮廓
        cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)
        
        # 创建半透明填充
        mask = np.zeros(overlay_img.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [centered_contour], 255)
        
        overlay = np.zeros_like(overlay_img)
        overlay[mask == 255] = [0, 255, 0]
        
        alpha = 0.3
        cv2.addWeighted(overlay, alpha, overlay_img, 1 - alpha, 0, overlay_img)

        return overlay_img

    def _create_square_image(self, image):
        """创建正方形图像"""
        h, w = image.shape[:2]
        square_size = max(h, w)
        square_img = np.zeros((square_size, square_size, 3), dtype=np.uint8)

        if h > w:
            scale = square_size / h
            new_width = int(w * scale)
            resized = cv2.resize(image, (new_width, square_size))
            start_x = (square_size - new_width) // 2
            square_img[:, start_x:start_x+new_width] = resized
        else:
            scale = square_size / w  
            new_height = int(h * scale)
            resized = cv2.resize(image, (square_size, new_height))
            start_y = (square_size - new_height) // 2
            square_img[start_y:start_y+new_height, :] = resized

        return square_img

    def _add_animal_contour(self, image, result, json_data):
        """为动物图像添加轮廓"""
        overlay = image.copy()
        
        # 尝试从JSON数据获取掩码
        if json_data:
            animal_mask = self.db_manager.get_animal_mask_from_json(
                json_data, result.get('category', '')
            )
            
            if animal_mask is not None:
                # 调整掩码尺寸
                h, w = image.shape[:2]
                resized_mask = cv2.resize(animal_mask, (w, h))
                
                # 提取轮廓
                resized_mask = (resized_mask * 255).astype(np.uint8)
                contours, _ = cv2.findContours(resized_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if contours:
                    max_contour = max(contours, key=cv2.contourArea)
                    cv2.drawContours(overlay, [max_contour], -1, (0, 255, 0), 2)
                    
                    # 半透明填充
                    mask = np.zeros(overlay.shape[:2], dtype=np.uint8)
                    cv2.fillPoly(mask, [max_contour], 255)
                    overlay_layer = np.zeros_like(overlay)
                    overlay_layer[mask == 255] = [0, 255, 0]
                    cv2.addWeighted(overlay_layer, 0.3, overlay, 0.7, 0, overlay)
        
        return overlay

    def _create_html_result(self, image_path, human_contour, results):
        """创建HTML结果页面（简化版）"""
        if not results:
            return None

        result = results[0]
        animal_category = result['category']
        similarity_score = int(result['similarity_score'] * 100)

        # 保存结果图像
        self._save_contour_images(image_path, human_contour, result)

        # 简单的HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>匹配结果</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; margin: 50px; }}
        .result {{ display: flex; justify-content: space-around; align-items: center; }}
        .score {{ font-size: 48px; font-weight: bold; color: #4CAF50; }}
        img {{ max-width: 300px; max-height: 300px; border: 2px solid #ddd; border-radius: 8px; }}
    </style>
</head>
<body>
    <h1>动物姿态匹配结果</h1>
    <div class="result">
        <div>
            <h3>您的姿势</h3>
            <img src="human_pose.jpg" alt="人体姿势">
        </div>
        <div>
            <div class="score">{similarity_score}%</div>
            <p>匹配度</p>
        </div>
        <div>
            <h3>{animal_category}</h3>
            <img src="animal_pose.jpg" alt="动物姿势">
        </div>
    </div>
</body>
</html>"""

        html_path = str(self.results_dir / "result.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return html_path

    def _save_contour_images(self, image_path, human_contour, result):
        """保存轮廓图像"""
        try:
            # 保存人体图像
            human_img_path = str(self.results_dir / "human_pose.jpg")
            query_img = cv2.imread(str(image_path))
            query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)
            cropped_img = self.image_processor.crop_to_square(query_img)
            overlay_img = self._create_contour_overlay(cropped_img, human_contour)
            plt.imsave(human_img_path, overlay_img)

            # 保存动物图像
            animal_img_path = str(self.results_dir / "animal_pose.jpg")
            img_path, json_data = self.db_manager.get_animal_image_info(
                result.get('category'), result.get('image_id')
            )
            
            if img_path:
                animal_img = cv2.imread(str(img_path))
                animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)
                square_img = self._create_square_image(animal_img)
                animal_overlay = self._add_animal_contour(square_img, result, json_data)
                plt.imsave(animal_img_path, animal_overlay)

            return human_img_path, animal_img_path

        except Exception as e:
            print(f"保存轮廓图像失败: {e}")
            return None, None

    @property 
    def human_model(self):
        """提供对人体模型的访问"""
        return self.image_processor.human_model 