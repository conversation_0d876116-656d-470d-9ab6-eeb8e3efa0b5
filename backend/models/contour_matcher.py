#!/usr/bin/env python3
"""
轮廓匹配核心模块
负责轮廓特征提取、相似度计算和匹配算法
"""

import cv2
import numpy as np
import pickle
from pathlib import Path
from scipy.spatial import KDTree


class ContourMatcher:
    """核心轮廓匹配器"""
    
    def __init__(self, animal_db_path):
        """初始化匹配器"""
        self.animal_db_path = Path(animal_db_path)
        self.animal_db = None
        self.features = None
        self.kdtree = None
        
        # 加载数据库
        self.load_database()
        
    def load_database(self):
        """加载动物轮廓数据库"""
        try:
            with open(self.animal_db_path, 'rb') as f:
                self.animal_db = pickle.load(f)
            self.build_index()
            print(f"动物数据库加载成功，包含 {len(self.animal_db['contours'])} 个轮廓")
        except Exception as e:
            print(f"无法加载动物轮廓数据库: {e}")
            raise
            
    def build_index(self):
        """构建KD-tree索引用于快速检索"""
        # 计算每个轮廓的特征
        self.features = []
        for contour in self.animal_db['contours']:
            features = self.compute_shape_features(contour)
            self.features.append(features)

        # 转换特征列表为numpy数组
        self.features = np.array(self.features)

        # 处理无限值和NaN值
        self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)

        # 构建KD-tree
        self.kdtree = KDTree(self.features)

    def compute_shape_features(self, contour):
        """计算轮廓的形状特征"""
        # 计算Hu矩
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            hu_moments = cv2.HuMoments(moments).flatten()
        else:
            hu_moments = np.zeros(7)

        # 安全的对数变换
        log_hu = np.zeros_like(hu_moments)
        for i, hu in enumerate(hu_moments):
            if abs(hu) > 1e-7:  # 避免对零取对数
                log_hu[i] = -np.sign(hu) * np.log10(abs(hu))
            else:
                log_hu[i] = 0

        # 计算面积和周长比值
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        if perimeter > 0:
            circularity = 4 * np.pi * area / (perimeter * perimeter)
        else:
            circularity = 0

        # 计算轮廓方向
        if len(contour) >= 5:  # 需要至少5个点来拟合椭圆
            try:
                (x, y), (ma, mi), angle = cv2.fitEllipse(contour)
                aspect_ratio = ma / mi if mi > 0 else 1.0
            except:
                aspect_ratio = 1.0
                angle = 0
        else:
            aspect_ratio = 1.0
            angle = 0

        # 组合特征
        features = np.concatenate([
            log_hu,  # Hu矩
            [circularity],  # 圆形度
            [aspect_ratio],  # 长宽比
            [np.sin(angle * np.pi / 180), np.cos(angle * np.pi / 180)]  # 方向（正弦和余弦）
        ])

        # 处理无限值和NaN值
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

        return features

    def find_matches(self, human_contour, top_k=1):
        """根据人体轮廓查找匹配的动物轮廓"""
        human_features = self.compute_shape_features(human_contour)

        try:
            distances, indices = self.kdtree.query(human_features, k=top_k)
        except Exception:
            return []

        # 使用 np.atleast_1d 确保结果是可迭代的数组
        distances = np.atleast_1d(distances)
        indices = np.atleast_1d(indices)

        # 健壮性检查：确保长度匹配
        if len(distances) != len(indices):
            return []

        results = []
        # 检查必要的键
        required_keys = ['categories', 'image_ids', 'image_paths', 'contours']
        for key in required_keys:
            if key not in self.animal_db or not isinstance(self.animal_db[key], (list, np.ndarray)):
                return []

        # 获取列表长度进行边界检查
        num_items = len(self.animal_db['contours'])
        for dist, idx in zip(distances, indices):
            # 转换索引为标准 Python int
            try:
                current_idx = int(idx)
            except (ValueError, TypeError):
                continue

            # 边界检查
            if 0 <= current_idx < num_items:
                try:
                    category = self.animal_db['categories'][current_idx] if current_idx < len(self.animal_db['categories']) else "Unknown"
                    image_id = self.animal_db['image_ids'][current_idx] if current_idx < len(self.animal_db['image_ids']) else ""
                    image_path = self.animal_db['image_paths'][current_idx] if current_idx < len(self.animal_db['image_paths']) else ""
                    contour = self.animal_db['contours'][current_idx]

                    results.append({
                        'category': category,
                        'image_id': image_id,
                        'image_path': image_path,
                        'contour': contour,
                        'similarity_score': 1.0 / (1.0 + dist),
                        'index': current_idx
                    })
                except (IndexError, Exception):
                    continue

        return results 