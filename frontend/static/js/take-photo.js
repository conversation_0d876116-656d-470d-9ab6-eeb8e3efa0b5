// 拍照页面JavaScript文件 - 拍照功能和倒计时

let isCapturing = false;
let countdownTimer = null;
let segmentationInterval = null;
let overlayCanvas = null;
let overlayCtx = null;

// 初始化拍照页面
function initTakePhotoPage() {
    console.log('初始化拍照页面...');
    
    // 绑定按钮事件
    bindButtons();
    
    // 初始化覆盖层画布
    initOverlayCanvas();
    
    // 确保摄像头正常工作
    setTimeout(() => {
        const video = document.getElementById('camera');
        if (video && !video.srcObject) {
            console.log('重新初始化摄像头...');
            initCamera();
        } else if (video && video.srcObject) {
            // 摄像头已经工作，开始实时分割
            startRealtimeSegmentation();
        }
        // 兜底：当视频可播放时再次启动
        if (video) {
            video.addEventListener('canplay', () => {
                console.log('video canplay，启动实时分割');
                startRealtimeSegmentation();
            }, { once: true });
        }
    }, 1000);
}

// 初始化覆盖层画布
function initOverlayCanvas() {
    overlayCanvas = document.getElementById('overlayCanvas');
    if (overlayCanvas) {
        overlayCtx = overlayCanvas.getContext('2d');
        console.log('覆盖层画布初始化成功');
        
        // 设置画布尺寸
        function resizeCanvas() {
            const video = document.getElementById('camera');
            if (video && video.videoWidth > 0 && video.videoHeight > 0) {
                const rect = video.getBoundingClientRect();
                overlayCanvas.width = rect.width;
                overlayCanvas.height = rect.height;
                console.log(`画布尺寸设置为: ${overlayCanvas.width}x${overlayCanvas.height}`);
                console.log(`视频尺寸: ${video.videoWidth}x${video.videoHeight}`);
            }
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', resizeCanvas);
        
        // 监听视频元数据加载
        const video = document.getElementById('camera');
        if (video) {
            video.addEventListener('loadedmetadata', resizeCanvas);
            video.addEventListener('canplay', resizeCanvas);
        }
        
        // 初始设置（延迟执行，等待视频加载）
        setTimeout(resizeCanvas, 1500);
        setTimeout(resizeCanvas, 3000); // 再次尝试
    } else {
        console.error('找不到overlayCanvas元素');
    }
}

// 开始实时分割
function startRealtimeSegmentation() {
    console.log('开始实时人体轮廓检测...');
    
    // 每500ms检测一次轮廓
    segmentationInterval = setInterval(() => {
        if (!isCapturing) {
            captureFrameForSegmentation();
        }
    }, 300);
}

// 捕获帧进行分割
function captureFrameForSegmentation() {
    const video = document.getElementById('camera');
    const canvas = document.createElement('canvas');
    
    if (!video || video.videoWidth === 0) return;
    
    // 设置临时画布
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    // 转换为blob并发送到后端
    canvas.toBlob(function(blob) {
        if (blob) {
            sendFrameForSegmentation(blob);
        }
    }, 'image/jpeg', 0.7);
}

// 发送帧到后端进行分割
function sendFrameForSegmentation(blob) {
    const formData = new FormData();
    formData.append('frame', blob, 'frame.jpg');
    
    fetch('/contour-only', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('轮廓检测响应:', data);
        if (data.success && data.contour) {
            drawContour(JSON.parse(data.contour));
        } else {
            console.log('未检测到轮廓或检测失败');
            // 清除之前的轮廓
            if (overlayCtx && overlayCanvas) {
                overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
            }
        }
    })
    .catch(error => {
        console.warn('轮廓检测失败:', error);
        // 清除之前的轮廓
        if (overlayCtx && overlayCanvas) {
            overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
        }
    });
}

// 绘制轮廓
function drawContour(contourPoints) {
    console.log('开始绘制轮廓，点数:', contourPoints.length);
    
    if (!overlayCtx || !overlayCanvas) {
        console.error('画布或上下文未初始化');
        return;
    }
    
    // 清除之前的绘制
    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
    
    if (!contourPoints || contourPoints.length === 0) {
        console.log('轮廓点为空');
        return;
    }
    
    const video = document.getElementById('camera');
    if (!video || video.videoWidth === 0) {
        console.error('视频元素未准备好');
        return;
    }
    
    // 计算映射（object-fit: cover 映射）
    const displayWidth = overlayCanvas.width;
    const displayHeight = overlayCanvas.height;
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;

    // cover: 放大到填满容器，可能被裁剪
    const scale = Math.max(displayWidth / videoWidth, displayHeight / videoHeight);
    const drawWidth = videoWidth * scale;
    const drawHeight = videoHeight * scale;
    const offsetX = (displayWidth - drawWidth) / 2;
    const offsetY = (displayHeight - drawHeight) / 2;

    console.log(`映射: scale=${scale.toFixed(3)}, offset=(${offsetX.toFixed(1)}, ${offsetY.toFixed(1)})`);
    console.log(`画布尺寸: ${displayWidth}x${displayHeight}`);
    console.log(`视频尺寸: ${videoWidth}x${videoHeight}`);

    // 如需镜像（前置摄像头），可将此开关改为 true，并在 x 计算处应用镜像
    const mirror = false; // 保持与视频同向（已取消CSS镜像）

    // 设置绘制样式
    overlayCtx.strokeStyle = '#00ff00'; // 绿色轮廓
    overlayCtx.lineWidth = 3;
    overlayCtx.fillStyle = 'rgba(0, 255, 0, 0.1)'; // 半透明绿色填充

    // 绘制轮廓
    overlayCtx.beginPath();

    for (let i = 0; i < contourPoints.length; i++) {
        const point = contourPoints[i];
        let x = point[0] * scale + offsetX;
        let y = point[1] * scale + offsetY;
        if (mirror) {
            x = displayWidth - x;
        }
        if (i === 0) {
            overlayCtx.moveTo(x, y);
        } else {
            overlayCtx.lineTo(x, y);
        }
    }
    
    overlayCtx.closePath();
    overlayCtx.fill();
    overlayCtx.stroke();
    
    console.log('轮廓绘制完成');
}

// 绑定按钮事件
function bindButtons() {
    const captureBtn = document.getElementById('captureBtn');
    const backBtn = document.getElementById('backBtn');
    
    if (captureBtn) {
        captureBtn.addEventListener('click', startCapture);
    }
    
    if (backBtn) {
        backBtn.addEventListener('click', goBack);
    }
}

// 开始拍照流程
function startCapture() {
    if (isCapturing) return;
    
    console.log('开始拍照流程...');
    isCapturing = true;
    
    // 隐藏按钮
    const footerControls = document.querySelector('.footer-controls');
    if (footerControls) {
        footerControls.style.display = 'none';
    }
    
    // 开始倒计时
    startCountdown();
}

// 开始倒计时
function startCountdown() {
    let count = 3;
    const countdownElement = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    
    if (countdownElement) {
        countdownElement.style.display = 'flex';
        countdownElement.textContent = count;
    }
    
    if (countdownText) {
        countdownText.style.display = 'block';
        countdownText.textContent = '准备拍照...';
    }
    
    countdownTimer = setInterval(() => {
        count--;
        
        if (count > 0) {
            if (countdownElement) {
                countdownElement.textContent = count;
            }
        } else {
            // 倒计时结束，拍照
            clearInterval(countdownTimer);
            capturePhoto();
        }
    }, 1000);
}

// 拍照
function capturePhoto() {
    console.log('正在拍照...');
    
    const video = document.getElementById('camera');
    const canvas = document.getElementById('captureCanvas');
    const countdownElement = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    
    if (!video || !canvas) {
        console.error('找不到视频或画布元素');
        resetCapture();
        return;
    }
    
    // 隐藏倒计时
    if (countdownElement) {
        countdownElement.style.display = 'none';
    }
    
    if (countdownText) {
        countdownText.textContent = '正在处理图像...';
    }
    
    // 设置画布尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // 绘制当前帧到画布
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    // 将画布内容转换为blob
    canvas.toBlob(function(blob) {
        if (blob) {
            uploadPhoto(blob);
        } else {
            console.error('无法创建图像blob');
            showError('拍照失败，请重试');
            resetCapture();
        }
    }, 'image/jpeg', 0.9);
}

// 上传照片
function uploadPhoto(blob) {
    console.log('上传照片...');
    
    const formData = new FormData();
    formData.append('image', blob, 'capture.jpg');
    
    showLoading('正在分析您的姿势...');
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('上传成功:', data);
        hideLoading();
        
        if (data.success) {
            // 跳转到结果页面
            window.location.href = `/result?image=${data.filename}`;
        } else {
            throw new Error(data.error || '处理失败');
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        hideLoading();
        showError('处理失败: ' + error.message);
        resetCapture();
    });
}

// 重置拍照状态
function resetCapture() {
    isCapturing = false;
    
    // 清除倒计时
    if (countdownTimer) {
        clearInterval(countdownTimer);
        countdownTimer = null;
    }
    
    // 隐藏倒计时元素
    const countdownElement = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    
    if (countdownElement) {
        countdownElement.style.display = 'none';
    }
    
    if (countdownText) {
        countdownText.style.display = 'none';
    }
    
    // 显示按钮
    const footerControls = document.querySelector('.footer-controls');
    if (footerControls) {
        footerControls.style.display = 'flex';
    }
}

// 返回首页
function goBack() {
    console.log('返回首页');
    window.location.href = '/';
}

// 页面特定的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('拍照页面加载完成');
    initTakePhotoPage();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (countdownTimer) {
        clearInterval(countdownTimer);
    }
    if (segmentationInterval) {
        clearInterval(segmentationInterval);
    }
});

// 键盘事件处理
document.addEventListener('keydown', function(event) {
    if (event.code === 'Space' && !isCapturing) {
        event.preventDefault();
        startCapture();
    } else if (event.code === 'Escape') {
        if (isCapturing) {
            resetCapture();
        } else {
            goBack();
        }
    }
}); 