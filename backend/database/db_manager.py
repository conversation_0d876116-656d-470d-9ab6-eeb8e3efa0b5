#!/usr/bin/env python3
"""
数据库管理模块
负责数据库连接、动物数据管理等
"""

import os
import json
import pickle
import random
import hashlib
from pathlib import Path


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.museum_dir = Path('/Users/<USER>/Desktop/museum')
        self.label_museum_dir = Path('/Users/<USER>/Desktop/label museum')
        
    def get_animal_image_info(self, target_category, target_image_id):
        """获取动物图像信息"""
        try:
            if not target_category and not target_image_id:
                print("错误: 结果中既没有类别也没有图像ID信息")
                return None, None

            if not self.museum_dir.exists() or not self.label_museum_dir.exists():
                print(f"严重错误: 博物馆目录未找到")
                return None, None

            # 步骤1: 先尝试通过image_id直接定位图片
            found_img_path = None

            if target_image_id:
                # 搜索所有可能的图片扩展名
                for ext in ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']:
                    potential_path = self.museum_dir / f"{target_image_id}{ext}"
                    if potential_path.exists():
                        found_img_path = potential_path
                        break

            # 如果通过ID没找到，则通过类别搜索
            if not found_img_path and target_category:
                # 获取所有有效的图片-JSON对
                valid_pairs = []
                for img_file in sorted(self.museum_dir.glob('*.*')):
                    if img_file.suffix.lower() not in ['.jpg', '.jpeg', '.png']:
                        continue

                    json_path = self.label_museum_dir / f"{img_file.stem}.json"
                    if json_path.exists():
                        try:
                            with open(json_path, 'r') as f:
                                label_data = json.load(f)

                            # 获取第一个shape的label
                            if 'shapes' in label_data and len(label_data['shapes']) > 0:
                                label = label_data['shapes'][0].get('label', '').lower()
                                if label == target_category.lower():
                                    valid_pairs.append((img_file, json_path, label))
                        except Exception:
                            continue

                # 如果找到匹配的图片，随机选择一个
                if valid_pairs:
                    # 使用image_id作为随机种子，确保结果可重现
                    seed = int(hashlib.md5(str(target_image_id).encode()).hexdigest(), 16) if target_image_id else 0
                    random.seed(seed)
                    selected_pair = random.choice(valid_pairs)
                    found_img_path = selected_pair[0]

            # 验证是否找到图片
            if not found_img_path:
                return None, None

            return found_img_path, self._get_json_annotation(found_img_path)

        except Exception as e:
            print(f"获取动物图像信息时出错: {e}")
            return None, None

    def _get_json_annotation(self, img_path):
        """获取图像的JSON标注信息"""
        json_path = self.label_museum_dir / f"{img_path.stem}.json"
        
        if not json_path.exists():
            return None
            
        try:
            with open(json_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取JSON标注失败: {e}")
            return None

    def get_animal_mask_from_json(self, json_data, target_animal_type):
        """从JSON数据中提取动物掩码"""
        if not json_data:
            return None
            
        try:
            img_h = json_data.get('imageHeight', 0)
            img_w = json_data.get('imageWidth', 0)
            
            if img_h <= 0 or img_w <= 0:
                return None
                
            import cv2
            import numpy as np
            
            # 创建掩码
            mask = np.zeros((img_h, img_w), dtype=np.uint8)
            
            # 处理所有匹配的形状
            shapes = json_data.get('shapes', [])
            for shape in shapes:
                if (shape.get('label', '').lower() == target_animal_type.lower() and 
                    shape.get('shape_type') == 'polygon'):
                    
                    points = shape.get('points', [])
                    if len(points) >= 3:
                        # 转换points为numpy数组
                        points = np.array(points, dtype=np.int32)
                        
                        # 根据形状类型填充掩码
                        if shape['shape_type'] == 'polygon':
                            cv2.fillPoly(mask, [points], 1)
                        elif shape['shape_type'] == 'rectangle':
                            cv2.rectangle(mask, tuple(points[0]), tuple(points[1]), 1, -1)
                            
            return mask
            
        except Exception as e:
            print(f"从JSON提取掩码时出错: {e}")
            return None

    def save_database_backup(self, db_data, backup_path):
        """保存数据库备份"""
        try:
            with open(backup_path, 'wb') as f:
                pickle.dump(db_data, f)
            return True
        except Exception as e:
            print(f"保存数据库备份失败: {e}")
            return False

    def load_database_backup(self, backup_path):
        """加载数据库备份"""
        try:
            with open(backup_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载数据库备份失败: {e}")
            return None

    def get_valid_image_pairs(self):
        """获取所有有效的图像-标注对"""
        valid_pairs = []
        
        for img_file in self.museum_dir.glob('*.*'):
            if img_file.suffix.lower() not in ['.jpg', '.jpeg', '.png']:
                continue
                
            json_path = self.label_museum_dir / f"{img_file.stem}.json"
            if json_path.exists():
                try:
                    with open(json_path, 'r') as f:
                        label_data = json.load(f)
                    
                    if 'shapes' in label_data and len(label_data['shapes']) > 0:
                        valid_pairs.append((img_file, json_path, label_data))
                except Exception:
                    continue
                    
        return valid_pairs

    def get_museum_location_info(self, animal_category):
        """获取动物在博物馆的位置信息"""
        location_map = {
            'giraffe': 'African Mammals, Floor 1, South Wing',
            'elephant': 'African Mammals, Floor 1, Central Hall', 
            'lion': 'African Predators, Floor 2, East Wing',
            'zebra': 'African Plains, Floor 1, West Wing',
            'rhino': 'Endangered Species, Floor 3, North Wing'
        }
        
        return location_map.get(animal_category.lower(), 'Main Exhibition Hall') 