/* 结果页面样式文件 - 按照图例设计 */

body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
}

.result-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 20px;
    gap: 40px;
    min-height: calc(100vh - 180px);
}

/* 图像区域 */
.image-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.image-container {
    width: min(450px, 40vw);
    height: min(600px, 65vh);
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.result-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.image-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.image-subtitle {
    font-size: 14px;
    color: #666;
    text-align: center;
}

/* 分数圆圈 */
.score-area {
    display: flex;
    align-items: center;
    justify-content: center;
}

.score-circle {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    background-color: #2c2c2c;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.score-text {
    color: white;
    font-size: 36px;
    font-weight: bold;
}

/* 底部区域 */
.bottom-section {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 20px 40px 30px;
    position: relative;
    height: 180px;
    flex-shrink: 0;
}

/* 动物信息卡片 */
.animal-info-card {
    background-color: #2c2c2c;
    color: white;
    border-radius: 12px;
    padding: 20px;
    width: 280px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
}

.card-description {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 15px;
    opacity: 0.9;
}

.more-info-btn {
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.more-info-btn:hover {
    opacity: 1;
}

/* 中间按钮区域 */
.center-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

.action-btn {
    background-color: #2c2c2c;
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    background-color: #404040;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.try-again-btn {
    background-color: white;
    color: #2c2c2c;
    border: 1px solid #ddd;
}

.try-again-btn:hover {
    background-color: #f8f8f8;
    border-color: #bbb;
}

/* 位置按钮 */
.location-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
}

.location-btn {
    background-color: #2c2c2c;
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.location-btn:hover {
    background-color: #404040;
    transform: translateY(-2px);
}

.pose-label {
    background-color: #999;
    color: white;
    border: none;
    border-radius: 15px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: default;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        gap: 30px;
        padding: 20px;
    }
    
    .image-container {
        width: 250px;
        height: 320px;
    }
    
    .bottom-section {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        text-align: center;
    }
    
    .animal-info-card {
        width: 100%;
        max-width: 400px;
    }
    
    .location-actions {
        align-items: center;
    }
}

@media (max-width: 768px) {
    .main-content {
        gap: 20px;
        padding: 15px;
    }
    
    .image-container {
        width: 200px;
        height: 280px;
    }
    
    .score-circle {
        width: 100px;
        height: 100px;
    }
    
    .score-text {
        font-size: 24px;
    }
    
    .center-actions {
        flex-direction: column;
        gap: 15px;
    }
} 