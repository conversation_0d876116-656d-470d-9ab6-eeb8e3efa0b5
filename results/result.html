<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animal Pose Matcher Result</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            text-align: center;
            margin-bottom: 40px;
        }
        h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .result {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
        }
        .result-column {
            flex: 1;
            text-align: center;
        }
        .match-score {
            width: 100px;
            height: 100px;
            line-height: 100px;
            background-color: #111;
            color: white;
            border-radius: 50%;
            margin: 0 auto;
            font-size: 24px;
            font-weight: bold;
        }
        img {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 20px;
        }
        .image-container {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            overflow: hidden;
            margin: 0 20px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        button {
            background-color: #111;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button.secondary {
            background-color: white;
            color: #111;
            border: 1px solid #111;
        }
    </style>
</head>
<body>
    <header>
    <div class="result">
        <div class="result-column">
            <h2>Cat</h2>
            <div class="image-container">
                <img src="/images/animal_pose.jpg" alt="Matching Animal">
            </div>
        </div>
        
        <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 20px;">
            <div class="match-score">67%</div>
            <p>Match Score</p>
            
            <!-- 添加两个新按钮 -->
            <button class="info-button" onclick="showAnimalInfo()" style="margin-top: 20px; background-color: #111; width: 180px;">Animal Information</button>
            <button class="guide-button" onclick="showMuseumMap()" style="margin-top: 10px; background-color: #111; width: 180px;">Guide Me There</button>
        </div>
        
        <div class="result-column">
            <h2>Your Pose</h2>
            <div class="image-container">
                <img src="/images/human_pose.jpg" alt="Your Pose">
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 40px;">
        <button class="secondary" onclick="window.location='/'">Try Again</button>
        <button onclick="openEmailModal()">Save Result</button>
    </div>

    <!-- 邮箱输入模态窗口 -->
    <div id="emailModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 15% auto; padding: 20px; border-radius: 10px; width: 80%; max-width: 500px;">
            <h2 style="margin-top: 0;">Send Result to Email</h2>
            <p>Please enter your email and we will send your match result to you:</p>
            <input type="email" id="emailInput" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;" placeholder="Your email address">
            <div style="text-align: right; margin-top: 20px;">
                <button style="background-color: #f0f0f0; color: #333; border: none; padding: 8px 16px; margin-right: 10px; border-radius: 4px; cursor: pointer;" onclick="closeEmailModal()">Cancel</button>
                <button style="background-color: #111; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" onclick="sendResultByEmail()">Send</button>
            </div>
        </div>
    </div>
    
    <!-- 动物信息模态窗口 -->
    <div id="animalInfoModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 10% auto; padding: 20px; border-radius: 10px; width: 90%; max-width: 800px; max-height: 80vh; overflow-y: auto;">
            <h2 style="margin-top: 0;" id="animalInfoTitle">Animal Information</h2>
            <div style="display: flex; flex-direction: column; align-items: center;">
                <img id="animalLargeImage" src="/images/animal_pose.jpg" alt="Animal" style="max-width: 100%; max-height: 400px; object-fit: contain; margin-bottom: 20px;">
                <div id="animalDescription">
                    <h3>About this animal</h3>
                    <p>XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX</p>
                    <p>XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX</p>
                    <p>XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX XXXXX</p>
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button style="background-color: #111; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" onclick="closeAnimalInfoModal()">Close</button>
            </div>
        </div>
    </div>
    
    <!-- 博物馆地图模态窗口 -->
    <div id="museumMapModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 5% auto; padding: 20px; border-radius: 10px; width: 95%; max-width: 1000px; max-height: 90vh; overflow-y: auto;">
            <h2 style="margin-top: 0;">Museum Map</h2>
            <div style="position: relative;">
                <img src="/images/museum_map.jpg" alt="Museum Map" style="width: 100%; height: auto; max-height: 600px; object-fit: contain;">
                
                <!-- 标记：观众位置 (蓝色) -->
                <div id="visitorLocation" style="position: absolute; left: 30%; top: 50%; width: 20px; height: 20px; background-color: blue; border-radius: 50%; transform: translate(-50%, -50%);">
                    <div style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); white-space: nowrap; background-color: rgba(255,255,255,0.8); padding: 2px 5px; border-radius: 3px; font-size: 12px;">You are here</div>
                </div>
                
                <!-- 标记：动物位置 (红色) -->
                <div id="animalLocation" style="position: absolute; left: 70%; top: 40%; width: 20px; height: 20px; background-color: red; border-radius: 50%; transform: translate(-50%, -50%);">
                    <div style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); white-space: nowrap; background-color: rgba(255,255,255,0.8); padding: 2px 5px; border-radius: 3px; font-size: 12px;" id="animalLocationLabel">Animal exhibit</div>
                </div>
                
                <!-- 路径线 (绿色虚线) -->
                <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                    <defs>
                        <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5"
                            markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                            <path d="M 0 0 L 10 5 L 0 10 z" fill="green"/>
                        </marker>
                    </defs>
                    <path id="guidePath" d="M 30% 50% L 50% 45% L 70% 40%" stroke="green" stroke-width="3" stroke-dasharray="5,5" fill="none" marker-end="url(#arrow)"/>
                </svg>
            </div>
            <div style="margin-top: 20px;">
                <p><strong>Directions:</strong> Head straight and turn right at the main hall. The <span id="animalExhibitName">animal</span> exhibit is located in the East Wing on Level 2.</p>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button style="background-color: #111; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" onclick="closeMuseumMapModal()">Close</button>
            </div>
        </div>
    </div>
    
    <script>
        // Store match result information
        const resultInfo = {
            animalCategory: "Bird",
            matchScore: "77",
            // Note: Current contour detection may identify shapes that don't perfectly match animals
            // This is a limitation of the current model and detection algorithm
        };
        
        function openEmailModal() {
            document.getElementById('emailModal').style.display = 'block';
        }
        
        function closeEmailModal() {
            document.getElementById('emailModal').style.display = 'none';
        }
        
        function sendResultByEmail() {
            const email = document.getElementById('emailInput').value;
            if (!email || !email.includes('@')) {
                alert('Please enter a valid email address');
                return;
            }
            
            // 发送邮箱地址到服务器
            fetch('/send-result', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    animalCategory: resultInfo.animalCategory,
                    matchScore: resultInfo.matchScore
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Saved!');
                } else {
                    alert('Send failed: ' + data.error);
                }
                closeEmailModal();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Send failed, please try again later');
                closeEmailModal();
            });
        }
        
        // 动物信息功能
        function showAnimalInfo() {
            // 更新动物名称标题
            document.getElementById('animalInfoTitle').textContent = resultInfo.animalCategory + ' Information';
            
            // 显示模态窗口
            document.getElementById('animalInfoModal').style.display = 'block';
        }
        
        function closeAnimalInfoModal() {
            document.getElementById('animalInfoModal').style.display = 'none';
        }
        
        // 博物馆导航功能
        function showMuseumMap() {
            // 更新动物位置标签
            document.getElementById('animalLocationLabel').textContent = resultInfo.animalCategory + ' exhibit';
            document.getElementById('animalExhibitName').textContent = resultInfo.animalCategory.toLowerCase();
            
            // 显示模态窗口
            document.getElementById('museumMapModal').style.display = 'block';
        }
        
        function closeMuseumMapModal() {
            document.getElementById('museumMapModal').style.display = 'none';
        }
        
        // 当页面加载时检查地图是否存在，如果不存在则使用占位图像
        window.addEventListener('load', function() {
            const mapImg = document.querySelector('#museumMapModal img');
            mapImg.onerror = function() {
                // 如果地图加载失败，使用占位图像
                this.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22600%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20width%3D%22800%22%20height%3D%22600%22%20fill%3D%22%23f8f9fa%22%2F%3E%3Ctext%20x%3D%22400%22%20y%3D%22300%22%20font-family%3D%22Arial%22%20font-size%3D%2236%22%20text-anchor%3D%22middle%22%20dominant-baseline%3D%22middle%22%3EMuseum%20Map%3C%2Ftext%3E%3C%2Fsvg%3E';
            };
        });
    </script>
</body>
</html>