<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匹配结果 - 动物姿态匹配器</title>
    <link rel="stylesheet" href="/static/css/result.css">
</head>
<body>
    <div class="result-page">
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧动物图像 -->
            <div class="image-area">
                <div class="image-title">{{animal_category}} with segmentation</div>
                <div class="image-container">
                    <img src="/images/animal_pose.jpg" alt="动物分割图" class="result-image">
                </div>
            </div>

            <!-- 中间分数区域 -->
            <div class="score-area">
                <div class="score-circle">
                    <span class="score-text">{{similarity_score}}%</span>
                </div>
            </div>

            <!-- 右侧人类图像 -->
            <div class="image-area">
                <div class="image-title">Your pose with segmentation</div>
                <div class="image-container">
                    <img src="/images/human_pose.jpg" alt="您的姿势分割图" class="result-image">
                </div>
            </div>
        </div>

        <!-- 底部区域 -->
        <div class="bottom-section">
            <!-- 左侧动物信息卡片 -->
            <div class="animal-info-card">
                <h2 class="card-title">{{animal_category}}</h2>
                <p class="card-description">The largest cat species in Asia, known for its distinctive striped coat and powerful hunting abilities.</p>
                <button class="more-info-btn" id="moreInfoBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                    </svg>
                    More Information
                </button>
            </div>

            <!-- 中间按钮区域 -->
            <div class="center-actions">
                <button class="action-btn try-again-btn" id="tryAgainBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="1 4 1 10 7 10"></polyline>
                        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                    </svg>
                    Try Again
                </button>
                <button class="action-btn" id="saveResultBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Save Result
                </button>
            </div>

            <!-- 右侧位置按钮 -->
            <div class="location-actions">
                <div class="pose-label">Your Pose</div>
                <button class="location-btn" id="findInMuseumBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    Find in East Wing
                </button>
            </div>
        </div>
    </div>

    <script src="/static/js/main.js"></script>
    <script src="/static/js/result.js"></script>
</body>
</html>
            <span class="location-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                </svg>
            </span>
            <span class="location-text">在博物馆中查找</span>
        </div>

        <!-- 您的姿势标签 -->
        <div class="pose-label">您的姿势</div>

        <!-- 控制按钮区域 -->
        <div class="control-area">
            <button class="control-btn" onclick="window.location='/'">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
                重新尝试
            </button>
            <button class="control-btn" onclick="saveResult()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                保存结果
            </button>
        </div>
    </div>

    <!-- 动物信息模态框 -->
    <div id="animalInfoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    {{animal_category}}
                    <span class="status-tag">易危</span>
                </h2>
                <button class="close-btn" onclick="hideAnimalInfo()">&times;</button>
            </div>
            <div class="modal-body">
                <h3 class="section-title">关于</h3>
                <p class="animal-description">
                    这种动物是我们博物馆收藏的一部分。参观展览了解更多关于它的栖息地、行为和保护状态的信息。
                </p>

                <div class="info-grid">
                    <div class="info-section">
                        <h3 class="section-title">食性</h3>
                        <p>因物种而异</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">栖息地</h3>
                        <p>非洲的天然栖息地</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">寿命</h3>
                        <p>野生环境中15-30年</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">体重</h3>
                        <p>因物种和年龄而异</p>
                    </div>
                </div>

                <div class="fun-fact">
                    <div class="fun-fact-title">有趣的事实</div>
                    <p>这种动物是我们博物馆重要保护工作的一部分。</p>
                </div>

                <h3 class="section-title">博物馆位置</h3>
                <div class="location-card">
                    <div class="location-header">
                        <div class="location-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                        </div>
                        <span>主展厅</span>
                    </div>
                    <p>请按指示牌或向博物馆导览员询问前往此展品的方向。</p>
                </div>

                <div class="map-placeholder">
                    博物馆地图占位符
                </div>

                <button class="close-button" onclick="hideAnimalInfo()">关闭</button>
            </div>
        </div>
    </div>

    <script src="/static/js/main.js"></script>
    <script src="/static/js/result.js"></script>
</body>
</html> 