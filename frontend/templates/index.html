<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动物姿态匹配器</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/index.css">
</head>
<body>
    <div class="main-container">
        <div class="left-panel">
            <div class="slideshow" id="slideshow">
                <img src="/static/images/homepage_1.jpg" class="active" alt="博物馆图像 1">
                <img src="/static/images/homepage_2.jpg" alt="博物馆图像 2">
                <img src="/static/images/homepage_3.jpg" alt="博物馆图像 3">
                <img src="/static/images/homepage_4.jpg" alt="博物馆图像 4">
                <img src="/static/images/homepage_5.jpg" alt="博物馆图像 5">
            </div>
            <div class="info-overlay">
                <h2>动物姿态匹配器</h2>
                <div class="step-item">
                    <div class="step-circle">1</div>
                    <div class="step-text">
                        <h3>摆姿势</h3>
                        <p>站在指定区域，像动物一样摆姿势</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-circle">2</div>
                    <div class="step-text">
                        <h3>拍照</h3>
                        <p>我们的摄像头将捕获并分析您的姿势</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-circle">3</div>
                    <div class="step-text">
                        <h3>查看结果</h3>
                        <p>发现您与哪种动物最匹配</p>
                    </div>
                </div>
                <button class="start-button" id="startExperience">
                    <span>开始体验</span> &rarr;
                </button>
            </div>
        </div>
        <div class="right-panel">
            <div class="camera-overlay">
                <div class="preview-label">实时预览</div>
                <video id="camera" class="camera-feed" autoplay></video>
                <canvas id="overlayCanvas"></canvas>
            </div>
        </div>
    </div>

    <script src="/static/js/main.js"></script>
    <script src="/static/js/index.js"></script>
</body>
</html> 