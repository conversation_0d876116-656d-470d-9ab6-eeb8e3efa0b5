// 结果页面JavaScript文件 - 处理交互功能

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('结果页面加载完成');
    initResultPage();
});

function initResultPage() {
    // 绑定按钮事件
    bindButtonEvents();
    
    // 添加动画效果
    addAnimations();
}

function bindButtonEvents() {
    // Try Again 按钮
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    if (tryAgainBtn) {
        tryAgainBtn.addEventListener('click', function() {
            console.log('重新开始');
            window.location.href = '/take-photo';
        });
    }
    
    // Save Result 按钮
    const saveResultBtn = document.getElementById('saveResultBtn');
    if (saveResultBtn) {
        saveResultBtn.addEventListener('click', function() {
            console.log('保存结果');
            saveResult();
        });
    }
    
    // Find in Museum 按钮
    const findInMuseumBtn = document.getElementById('findInMuseumBtn');
    if (findInMuseumBtn) {
        findInMuseumBtn.addEventListener('click', function() {
            console.log('在博物馆中查找');
            findInMuseum();
        });
    }
    
    // More Information 按钮
    const moreInfoBtn = document.getElementById('moreInfoBtn');
    if (moreInfoBtn) {
        moreInfoBtn.addEventListener('click', function() {
            console.log('显示更多信息');
            showAnimalInfo();
        });
    }
}

function addAnimations() {
    // 为分数圆圈添加动画
    const scoreCircle = document.querySelector('.score-circle');
    if (scoreCircle) {
        setTimeout(() => {
            scoreCircle.style.transform = 'scale(1.1)';
            setTimeout(() => {
                scoreCircle.style.transform = 'scale(1)';
            }, 200);
        }, 500);
    }
    
    // 为图像添加淡入动画
    const images = document.querySelectorAll('.result-image');
    images.forEach((img, index) => {
        img.style.opacity = '0';
        img.style.transform = 'translateY(20px)';
        setTimeout(() => {
            img.style.transition = 'all 0.5s ease';
            img.style.opacity = '1';
            img.style.transform = 'translateY(0)';
        }, 300 + index * 200);
    });
}

function saveResult() {
    // 创建下载链接
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 设置画布尺寸
    canvas.width = 1200;
    canvas.height = 800;
    
    // 绘制背景
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 获取页面信息
    const animalName = document.querySelector('.card-title')?.textContent || 'Unknown Animal';
    const score = document.querySelector('.score-text')?.textContent || '0%';
    
    // 绘制文字
    ctx.fillStyle = '#333';
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Animal Pose Match Result', canvas.width / 2, 100);
    
    ctx.font = 'bold 36px Arial';
    ctx.fillText(`${animalName} - ${score} Match`, canvas.width / 2, 200);
    
    ctx.font = '24px Arial';
    ctx.fillText('Generated by Animal Pose Matcher', canvas.width / 2, canvas.height - 50);
    
    // 下载图片
    const link = document.createElement('a');
    link.download = `animal_pose_result_${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
    
    // 显示成功消息
    showSuccessMessage('结果已保存到下载文件夹！');
}

function findInMuseum() {
    // 显示博物馆地图或位置信息
    const modal = createModal();
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h2>在博物馆中查找</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="text-align: center; padding: 40px;">
                    <h3>East Wing - Level 2</h3>
                    <p style="margin: 20px 0;">您可以在博物馆的东翼二楼找到这个动物展品。</p>
                    <div style="background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <p><strong>开放时间：</strong> 9:00 AM - 5:00 PM</p>
                        <p><strong>位置：</strong> 东翼二楼，展厅 E2-A</p>
                        <p><strong>最佳参观时间：</strong> 上午 10:00 - 12:00</p>
                    </div>
                    <button class="action-btn" onclick="closeModal()" style="margin-top: 20px;">
                        知道了
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function showAnimalInfo() {
    // 获取动物名称
    const animalName = document.querySelector('.card-title')?.textContent || 'Unknown Animal';
    
    // 显示加载状态
    showLoading('正在获取动物信息...');
    
    // 调用API获取动物信息
    fetch(`/api/animal-info?animal=${encodeURIComponent(animalName)}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayAnimalInfoModal(data.data);
            } else {
                showError('获取动物信息失败');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('获取动物信息失败:', error);
            showError('获取动物信息失败');
        });
}

function displayAnimalInfoModal(animalInfo) {
    const modal = createModal();
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${animalInfo.name}</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h3>基本信息</h3>
                        <p><strong>科学名称：</strong> ${animalInfo.scientific_name}</p>
                        <p><strong>栖息地：</strong> ${animalInfo.habitat}</p>
                        <p><strong>体重：</strong> ${animalInfo.weight}</p>
                        <p><strong>体长：</strong> ${animalInfo.length}</p>
                    </div>
                    <div>
                        <h3>特征</h3>
                        <p><strong>饮食：</strong> ${animalInfo.diet}</p>
                        <p><strong>寿命：</strong> ${animalInfo.lifespan}</p>
                        <p><strong>保护状态：</strong> ${animalInfo.conservation_status}</p>
                    </div>
                </div>
                <div>
                    <h3>有趣的事实</h3>
                    <ul>
                        ${animalInfo.interesting_facts.map(fact => `<li>${fact}</li>`).join('')}
                    </ul>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function createModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.cssText = `
        display: none;
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 1000;
        align-items: center;
        justify-content: center;
        padding: 20px;
        overflow-y: auto;
    `;
    return modal;
}

function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.remove();
    });
}

function showSuccessMessage(message) {
    const successMsg = document.createElement('div');
    successMsg.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(34, 197, 94, 0.9);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 9999;
        max-width: 300px;
        font-weight: 500;
    `;
    successMsg.textContent = message;
    document.body.appendChild(successMsg);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (successMsg.parentNode) {
            successMsg.remove();
        }
    }, 3000);
}

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    if (event.code === 'Space') {
        event.preventDefault();
        const tryAgainBtn = document.getElementById('tryAgainBtn');
        if (tryAgainBtn) tryAgainBtn.click();
    } else if (event.code === 'KeyS' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        const saveResultBtn = document.getElementById('saveResultBtn');
        if (saveResultBtn) saveResultBtn.click();
    } else if (event.code === 'Escape') {
        closeModal();
    }
}); 