// 首页JavaScript文件 - 图片轮播和交互功能

let currentSlide = 0;
let slideInterval = null;
const slideImages = [
    '/static/images/homepage_1.jpg',
    '/static/images/homepage_2.jpg', 
    '/static/images/homepage_3.jpg',
    '/static/images/homepage_4.jpg',
    '/static/images/homepage_5.jpg'
];

// 初始化图片轮播
function initSlideshow() {
    const slideshow = document.getElementById('slideshow');
    if (!slideshow) return;

    const images = slideshow.querySelectorAll('img');
    
    // 如果图片不存在，创建默认图片
    if (images.length === 0) {
        createDefaultSlides();
        return;
    }

    // 开始轮播
    startSlideshow();
}

// 创建默认幻灯片（如果图片文件不存在）
function createDefaultSlides() {
    const slideshow = document.getElementById('slideshow');
    const defaultColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'];
    const defaultTexts = ['动物世界', '姿态匹配', '智能识别', '互动体验', '精彩发现'];
    
    slideshow.innerHTML = '';
    
    defaultColors.forEach((color, index) => {
        const slide = document.createElement('div');
        slide.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, ${color}, ${color}aa);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            opacity: ${index === 0 ? 1 : 0};
            transition: opacity 1s ease-in-out;
        `;
        slide.textContent = defaultTexts[index];
        slide.classList.add('slide');
        if (index === 0) slide.classList.add('active');
        slideshow.appendChild(slide);
    });
    
    startSlideshow();
}

// 开始幻灯片轮播
function startSlideshow() {
    const slides = document.querySelectorAll('#slideshow img, #slideshow .slide');
    if (slides.length <= 1) return;
    
    slideInterval = setInterval(() => {
        // 移除当前活动状态
        slides[currentSlide].classList.remove('active');
        
        // 移动到下一张
        currentSlide = (currentSlide + 1) % slides.length;
        
        // 添加新的活动状态
        slides[currentSlide].classList.add('active');
    }, 3000); // 每3秒切换一次
}

// 停止幻灯片轮播
function stopSlideshow() {
    if (slideInterval) {
        clearInterval(slideInterval);
        slideInterval = null;
    }
}

// 处理开始体验按钮点击
function handleStartExperience() {
    const startBtn = document.getElementById('startExperience');
    if (!startBtn) return;
    
    startBtn.addEventListener('click', function() {
        console.log('开始体验按钮被点击');
        showLoading('正在启动摄像头...');
        
        // 跳转到拍照页面
        setTimeout(() => {
            window.location.href = '/take-photo';
        }, 1000);
    });
}

// 检查图片是否存在
function checkImageExists(src) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = src;
    });
}

// 预加载图片
async function preloadImages() {
    console.log('开始预加载图片...');
    
    for (let i = 0; i < slideImages.length; i++) {
        const exists = await checkImageExists(slideImages[i]);
        if (!exists) {
            console.warn(`图片不存在: ${slideImages[i]}`);
        }
    }
}

// 页面特定的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('首页初始化开始...');
    
    // 预加载图片
    preloadImages();
    
    // 初始化幻灯片
    setTimeout(() => {
        initSlideshow();
    }, 500);
    
    // 绑定开始体验按钮
    handleStartExperience();
    
    console.log('首页初始化完成');
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    stopSlideshow();
}); 