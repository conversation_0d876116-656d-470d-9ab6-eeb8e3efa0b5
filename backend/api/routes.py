#!/usr/bin/env python3
"""
HTTP路由处理模块
负责处理所有API请求和路由分发
"""

import os
import json
import base64
import uuid
import time
import cv2
import numpy as np
from pathlib import Path
import http.server
import urllib.parse
import traceback


class RouteHandler:
    """路由处理器"""
    
    def __init__(self, retrieval_system, template_manager, animal_info_api):
        """初始化路由处理器"""
        self.retrieval_system = retrieval_system
        self.template_manager = template_manager
        self.animal_info_api = animal_info_api
        self.latest_image_path = None
        self.latest_results = None

    def handle_get_request(self, handler, parsed_path, query_params):
        """处理GET请求"""
        path = parsed_path.path
        
        # 主页
        if path == '/' or path == '/index.html':
            self._serve_template(handler, 'index.html')
            
        # 拍照页面
        elif path == '/take-photo' or path == '/take-photo.html':
            self._serve_template(handler, 'take-photo.html')
            
        # 结果页面
        elif path == '/result' or path == '/result.html':
            self._handle_result_page(handler)
            
        # 动物信息API
        elif path == '/api/animal-info':
            self._handle_animal_info_api(handler, query_params)
            
        # 图片服务
        elif path.startswith('/images/'):
            self._serve_image(handler, path)
            
        # 检查结果状态
        elif path == '/check-results':
            self._handle_check_results(handler)
            
        else:
            handler.send_error(404, "Page not found")

    def handle_post_request(self, handler):
        """处理POST请求"""
        if handler.path == '/contour-only':
            self._handle_contour_only(handler)
        elif handler.path == '/capture':
            self._handle_capture(handler)
        elif handler.path == '/upload':
            self._handle_upload(handler)
        elif handler.path == '/send-result':
            self._handle_send_result(handler)
        else:
            handler.send_error(404, "API not found")

    def _serve_template(self, handler, template_name):
        """提供HTML模板"""
        try:
            html_content = self.template_manager.get_template(template_name)
            handler.send_response(200)
            handler.send_header('Content-type', 'text/html; charset=utf-8')
            handler.end_headers()
            handler.wfile.write(html_content.encode('utf-8'))
        except Exception as e:
            print(f"模板服务错误: {e}")
            handler.send_error(500, f"模板错误: {str(e)}")

    def _handle_result_page(self, handler):
        """处理结果页面请求"""
        if self.latest_results and self.latest_image_path:
            # 确保结果图像存在
            human_img_path = self.retrieval_system.results_dir / "human_pose.jpg"
            animal_img_path = self.retrieval_system.results_dir / "animal_pose.jpg"

            if not os.path.exists(human_img_path) or not os.path.exists(animal_img_path):
                print("生成结果图像...")
                # 重新生成图像
                self.retrieval_system._save_contour_images(
                    self.latest_image_path,
                    human_contour=self.retrieval_system.extract_human_contour(self.latest_image_path),
                    result=self.latest_results[0]
                )

            # 生成结果页面HTML
            html_content = self.template_manager.get_result_template(
                self.latest_results,
                self.latest_results[0]['similarity_score']
            )
            handler.send_response(200)
            handler.send_header('Content-type', 'text/html; charset=utf-8')
            handler.end_headers()
            handler.wfile.write(html_content.encode('utf-8'))
        else:
            # 如果没有结果，重定向到主页
            handler.send_response(302)
            handler.send_header('Location', '/')
            handler.end_headers()

    def _handle_animal_info_api(self, handler, query_params):
        """处理动物信息API请求"""
        try:
            print(f"收到动物信息API请求")
            print(f"查询参数: {query_params}")

            # 解析查询参数
            if 'name' not in query_params:
                self._send_json_error(handler, 400, "缺少动物名称参数")
                return

            animal_name = query_params['name'][0]
            print(f"请求动物信息: {animal_name}")

            # 获取动物信息
            animal_info = self.animal_info_api.get_animal_info(animal_name)
            print(f"获取到动物信息: {animal_info}")

            # 发送响应
            self._send_json_response(handler, animal_info)

        except Exception as e:
            print(f"处理动物信息API请求时出错: {str(e)}")
            traceback.print_exc()
            self._send_json_error(handler, 500, f"获取动物信息时出错: {str(e)}")

    def _serve_image(self, handler, path):
        """提供图片服务"""
        image_name = path.split('/')[-1]

        try:
            # 检查首页幻灯片图像
            if image_name.startswith('homepage_'):
                self._serve_homepage_image(handler, image_name)
                return
                
            # 检查结果图像
            elif image_name in ["human_pose.jpg", "animal_pose.jpg"]:
                self._serve_result_image(handler, image_name)
                return
                
            else:
                # 常规图像请求
                image_path = self.retrieval_system.results_dir / image_name
                if os.path.exists(image_path):
                    self._send_image_file(handler, image_path)
                else:
                    handler.send_error(404, f"图像未找到: {image_name}")

        except Exception as e:
            print(f"图片服务错误: {e}")
            handler.send_error(500, f"图片服务错误: {str(e)}")

    def _serve_homepage_image(self, handler, image_name):
        """提供首页图像"""
        homepage_pic_dir = Path('/Users/<USER>/Desktop/homepage pic')
        
        # 获取所有图片文件
        homepage_files = list(homepage_pic_dir.glob('*.jp*g'))
        homepage_files.extend(list(homepage_pic_dir.glob('*.JP*G')))

        if not homepage_files:
            handler.send_error(404, f"未找到首页图像")
            return

        # 获取索引
        try:
            index = int(image_name.split('_')[1].split('.')[0]) - 1
            if index < 0:
                index = 0
            if index >= len(homepage_files):
                index = len(homepage_files) - 1
            image_path = homepage_files[index]
        except:
            image_path = homepage_files[0]

        self._send_image_file(handler, image_path)

    def _serve_result_image(self, handler, image_name):
        """提供结果图像"""
        image_path = self.retrieval_system.results_dir / image_name
        print(f"结果图像请求: {image_name}, 路径: {image_path}, 存在: {os.path.exists(image_path)}")

        # 如果图像不存在，尝试重新生成
        if not os.path.exists(image_path) and self.latest_results and self.latest_image_path:
            print(f"图像 {image_path} 不存在，尝试重新生成...")
            try:
                human_contour = self.retrieval_system.extract_human_contour(self.latest_image_path)
                self.retrieval_system._save_contour_images(
                    self.latest_image_path,
                    human_contour=human_contour,
                    result=self.latest_results[0]
                )
                print(f"重新生成图像，现在存在: {os.path.exists(image_path)}")
            except Exception as e:
                print(f"重新生成图像失败: {e}")

        if os.path.exists(image_path):
            self._send_image_file(handler, image_path)
        else:
            # 发送占位图像
            blank_img = np.ones((300, 400, 3), dtype=np.uint8) * 255
            cv2.putText(blank_img, f"Image not found", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            handler.send_response(200)
            handler.send_header('Content-type', 'image/jpeg')
            handler.end_headers()
            
            _, img_encoded = cv2.imencode('.jpg', blank_img)
            handler.wfile.write(img_encoded.tobytes())

    def _send_image_file(self, handler, image_path):
        """发送图像文件"""
        print(f"发送图像文件: {image_path}")
        handler.send_response(200)
        
        # 根据扩展名确定内容类型
        if str(image_path).lower().endswith(('.jpg', '.jpeg')):
            handler.send_header('Content-type', 'image/jpeg')
        elif str(image_path).lower().endswith('.png'):
            handler.send_header('Content-type', 'image/png')
        else:
            handler.send_header('Content-type', 'image/jpeg')
            
        handler.end_headers()
        
        with open(image_path, 'rb') as f:
            handler.wfile.write(f.read())

    def _handle_check_results(self, handler):
        """处理检查结果状态请求"""
        ready = (self.latest_results is not None and self.latest_image_path is not None)
        self._send_json_response(handler, {'ready': ready})

    def _handle_contour_only(self, handler):
        """处理仅轮廓检测请求"""
        try:
            # 检查内容类型
            content_type = handler.headers.get('Content-Type', '')
            content_length = int(handler.headers['Content-Length'])
            post_data = handler.rfile.read(content_length)
            
            if content_type.startswith('multipart/form-data'):
                # 处理FormData格式
                boundary = content_type.split('boundary=')[1].encode()
                parts = post_data.split(b'--' + boundary)
                
                img_data = None
                for part in parts:
                    if b'Content-Disposition: form-data; name="frame"' in part:
                        # 找到文件数据
                        header_end = part.find(b'\r\n\r\n')
                        if header_end != -1:
                            img_data = part[header_end + 4:]
                            # 移除结尾的换行符
                            if img_data.endswith(b'\r\n'):
                                img_data = img_data[:-2]
                            break
                
                if not img_data:
                    self._send_json_error(handler, 400, "No frame data found")
                    return
                    
            else:
                # 处理JSON格式（原有逻辑）
                data = json.loads(post_data.decode('utf-8'))
                if 'image' not in data:
                    self._send_json_error(handler, 400, "未提供图像数据")
                    return
                # 解码Base64图像
                img_data = base64.b64decode(data['image'].split(',')[1])
            
            # 生成临时文件名
            temp_filename = f"temp_frame_{uuid.uuid4()}.jpg"
            temp_path = self.retrieval_system.results_dir / temp_filename

            # 保存临时图像
            with open(temp_path, 'wb') as f:
                f.write(img_data)

            # 提取人体轮廓但不执行检索
            try:
                contour_data = self._extract_contour_for_realtime(temp_path)
                if contour_data:
                    self._send_json_response(handler, contour_data)
                else:
                    self._send_json_response(handler, {
                        'success': False,
                        'error': '未检测到人体'
                    })
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        except Exception as e:
            self._send_json_error(handler, 500, str(e))

    def _handle_capture(self, handler):
        """处理拍照请求"""
        try:
            content_length = int(handler.headers['Content-Length'])
            post_data = handler.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            if 'image' not in data:
                self._send_json_error(handler, 400, "未提供图像数据")
                return

            # 解码Base64图像
            img_data = base64.b64decode(data['image'].split(',')[1])

            # 生成唯一时间戳文件名
            timestamp = int(time.time())
            img_filename = f"web_capture_{timestamp}.jpg"
            img_path = self.retrieval_system.results_dir / img_filename

            # 保存图像
            with open(img_path, 'wb') as f:
                f.write(img_data)

            print(f"Web拍照保存到 {img_path}")

            # 清理旧的拍照图像
            self._cleanup_old_captures(img_path)

            # 处理图像并检索相似姿态
            try:
                error_message = self._process_captured_image(img_path)
                if error_message:
                    self._send_json_response(handler, {
                        'success': False,
                        'error': error_message
                    })
                else:
                    self._send_json_response(handler, {
                        'success': True,
                        'redirect': '/result.html'
                    })
            except Exception as e:
                traceback.print_exc()
                self._send_json_error(handler, 500, str(e))

        except Exception as e:
            traceback.print_exc()
            self._send_json_error(handler, 500, f"处理图像时出错: {str(e)}")

    def _handle_upload(self, handler):
        """处理文件上传请求（用于拍照功能）"""
        try:
            # 获取内容类型和边界
            content_type = handler.headers['Content-Type']
            if not content_type.startswith('multipart/form-data'):
                self._send_json_error(handler, 400, "Invalid content type")
                return
            
            # 读取POST数据
            content_length = int(handler.headers['Content-Length'])
            post_data = handler.rfile.read(content_length)
            
            # 解析multipart数据
            boundary = content_type.split('boundary=')[1].encode()
            parts = post_data.split(b'--' + boundary)
            
            image_data = None
            for part in parts:
                if b'Content-Disposition: form-data; name="image"' in part:
                    # 找到文件数据
                    header_end = part.find(b'\r\n\r\n')
                    if header_end != -1:
                        image_data = part[header_end + 4:]
                        # 移除结尾的换行符
                        if image_data.endswith(b'\r\n'):
                            image_data = image_data[:-2]
                        break
            
            if not image_data:
                self._send_json_error(handler, 400, "No image data found")
                return
            
            # 生成唯一文件名
            timestamp = int(time.time())
            filename = f"capture_{timestamp}.jpg"
            img_path = self.retrieval_system.results_dir / filename
            
            # 保存图像
            with open(img_path, 'wb') as f:
                f.write(image_data)
            
            print(f"图像上传保存到: {img_path}")
            
            # 清理旧的拍照图像
            self._cleanup_old_captures(img_path)
            
            # 处理图像并检索相似姿态
            try:
                error_message = self._process_captured_image(img_path)
                if error_message:
                    self._send_json_response(handler, {
                        'success': False,
                        'error': error_message
                    })
                else:
                    self._send_json_response(handler, {
                        'success': True,
                        'filename': filename,
                        'message': 'Image processed successfully'
                    })
            except Exception as e:
                traceback.print_exc()
                self._send_json_error(handler, 500, str(e))
                
        except Exception as e:
            traceback.print_exc()
            self._send_json_error(handler, 500, f"Upload failed: {str(e)}")

    def _handle_send_result(self, handler):
        """处理发送结果请求"""
        try:
            content_length = int(handler.headers['Content-Length'])
            post_data = handler.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            if 'email' not in data:
                self._send_json_error(handler, 400, "需要邮箱地址")
                return

            email = data['email']
            print(f"收到邮件发送请求: {email}")
            print(f"动物类别: {data.get('animalCategory', 'Unknown')}")
            print(f"匹配分数: {data.get('matchScore', 'Unknown')}")

            # 返回成功响应（在生产环境中应集成真实的邮件发送服务）
            self._send_json_response(handler, {
                'success': True,
                'message': f'结果已发送到您的邮箱！'
            })

        except Exception as e:
            self._send_json_error(handler, 400, "邮件发送失败")

    def _extract_contour_for_realtime(self, temp_path):
        """为实时检测提取轮廓"""
        try:
            print(f"开始处理实时轮廓检测: {temp_path}")
            # 使用YOLO模型检测人体
            results = self.retrieval_system.human_model(temp_path, verbose=False)

            if results and results[0].masks:
                print(f"检测到 {len(results[0].masks)} 个掩码")

                # 读取原图像获取尺寸
                img = cv2.imread(str(temp_path))
                img_h, img_w = img.shape[:2]
                print(f"原图尺寸: {img_w} × {img_h}")

                # 获取人体掩码
                person_masks = []
                for i, r in enumerate(results[0].boxes.data):
                    if int(r[5]) == 0:  # 0代表COCO数据集中的人
                        if i < len(results[0].masks.data):
                            confidence = float(r[4])
                            mask = results[0].masks.data[i].cpu().numpy()
                            mask_h, mask_w = mask.shape
                            print(f"原始掩码尺寸: {mask_w} × {mask_h}")
                            person_masks.append((confidence, mask))
                            print(f"找到人体掩码 {i+1}，置信度: {confidence:.3f}")

                if person_masks:
                    print(f"找到 {len(person_masks)} 个人体掩码")
                    # 使用置信度最高的掩码
                    best_mask = max(person_masks, key=lambda x: x[0])
                    mask = best_mask[1]
                    print(f"使用置信度最高的掩码: {best_mask[0]:.3f}")

                    # 将掩码缩放到原图尺寸
                    mask_h, mask_w = mask.shape
                    print(f"缩放掩码: {mask_w}×{mask_h} -> {img_w}×{img_h}")
                    resized_mask = cv2.resize(mask, (img_w, img_h))

                    # 提取轮廓
                    resized_mask = (resized_mask * 255).astype(np.uint8)
                    contours, _ = cv2.findContours(resized_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    if contours:
                        print(f"提取到 {len(contours)} 个轮廓")
                        # 获取最大轮廓
                        max_contour = max(contours, key=cv2.contourArea)
                        contour_area = cv2.contourArea(max_contour)
                        print(f"最大轮廓面积: {contour_area}")

                        # 获取轮廓边界框
                        x, y, w, h = cv2.boundingRect(max_contour)

                        # 简化轮廓并转换为2D点列表
                        epsilon = 0.005 * cv2.arcLength(max_contour, True)
                        simplified_contour = cv2.approxPolyDP(max_contour, epsilon, True)

                        # 转换为2D点列表 [[x,y], [x,y], ...]
                        contour_points = [[int(point[0][0]), int(point[0][1])] for point in simplified_contour]
                        print(f"简化后轮廓点数: {len(contour_points)}")

                        return {
                            'success': True,
                            'contour_points': contour_points,
                            'image_width': img_w,
                            'image_height': img_h,
                            'bbox': [x, y, w, h],
                            'orig_shape': [img_h, img_w],  # 添加原图尺寸信息
                            'mask_shape': [mask_h, mask_w]  # 添加掩码尺寸信息
                        }
                    else:
                        print("未找到轮廓")
                else:
                    print("未找到人体掩码")
            else:
                print("YOLO模型未检测到掩码")
            return None

        except Exception as e:
            print(f"实时轮廓提取错误: {e}")
            return None

    def _process_captured_image(self, img_path):
        """处理拍照的图像"""
        try:
            # 提取人体轮廓
            human_contour = self.retrieval_system.extract_human_contour(img_path)

            if human_contour is not None:
                # 检索相似姿态
                results = self.retrieval_system.retrieve(human_contour, top_k=1)

                # 调试输出
                if results and len(results) > 0:
                    result = results[0]
                    print(f"检索结果: 类别='{result.get('category')}', "
                          f"图像ID='{result.get('image_id')}', "
                          f"索引={result.get('index')}, "
                          f"相似度={result.get('similarity_score', 0) * 100:.1f}%")

                # 保存结果用于显示
                self.latest_image_path = img_path
                self.latest_results = results

                if results and len(results) > 0:
                    # 确保生成结果图像
                    human_img_path, animal_img_path = self.retrieval_system._save_contour_images(
                        img_path,
                        human_contour=human_contour,
                        result=results[0]
                    )

                    # 检查图像是否生成
                    if (human_img_path and animal_img_path and 
                        os.path.exists(human_img_path) and os.path.exists(animal_img_path)):
                        print(f"结果图像生成: {human_img_path}, {animal_img_path}")
                        return None  # 成功
                    else:
                        return "生成结果图像失败"
                else:
                    return "未找到匹配结果"
            else:
                return "图像中未检测到人体"

        except Exception as e:
            print(f"处理拍照图像错误: {e}")
            return str(e)

    def _cleanup_old_captures(self, current_path):
        """清理旧的拍照文件"""
        for old_file in self.retrieval_system.results_dir.glob("web_capture_*.jpg"):
            if old_file != current_path and os.path.exists(old_file):
                try:
                    os.remove(old_file)
                except:
                    pass

    def _send_json_response(self, handler, data):
        """发送JSON响应"""
        handler.send_response(200)
        handler.send_header('Content-type', 'application/json; charset=utf-8')
        handler.send_header('Access-Control-Allow-Origin', '*')
        handler.end_headers()
        response_json = json.dumps(data, ensure_ascii=False)
        handler.wfile.write(response_json.encode('utf-8'))

    def _send_json_error(self, handler, status_code, error_message):
        """发送JSON错误响应"""
        handler.send_response(status_code)
        handler.send_header('Content-type', 'application/json; charset=utf-8')
        handler.send_header('Access-Control-Allow-Origin', '*')
        handler.end_headers()
        handler.wfile.write(json.dumps({
            "success": False,
            "error": error_message
        }, ensure_ascii=False).encode('utf-8')) 