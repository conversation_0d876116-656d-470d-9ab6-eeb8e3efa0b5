#!/usr/bin/env python3
"""
图像处理模块
负责人体轮廓提取、图像预处理、动物检测等
"""

import os
import cv2
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from ultralytics import YOLO


class ImageProcessor:
    """图像处理器"""
    
    def __init__(self, human_model_path):
        """初始化图像处理器"""
        self.human_model_path = Path(human_model_path)
        
        # 加载人体分割模型
        try:
            self.human_model = YOLO(str(self.human_model_path))
            print("人体分割模型加载成功")
        except Exception as e:
            print(f"无法加载人体分割模型: {e}")
            raise
            
        # 尝试加载动物识别模型（可选）
        try:
            self.animal_recognition_model = YOLO('yolov8n.pt')
        except Exception:
            self.animal_recognition_model = None

    def extract_human_contour(self, image_path):
        """从人体图像中提取轮廓，并进行精度改进"""
        # 使用YOLO模型进行预测
        results = self.human_model(image_path, verbose=False)

        # 获取分割掩码
        if not results or not results[0].masks:
            raise ValueError("未检测到人体轮廓")

        # 获取人体掩码（选择置信度最高的人体检测结果）
        person_masks = []
        for i, r in enumerate(results[0].boxes.data):
            if int(r[5]) == 0:  # 0代表COCO数据集中的人
                if i < len(results[0].masks.data):
                    confidence = float(r[4])
                    mask = results[0].masks.data[i].cpu().numpy()
                    person_masks.append((confidence, mask))

        if not person_masks:
            raise ValueError("未检测到人体轮廓")

        # 使用置信度最高的人体掩码
        mask = max(person_masks, key=lambda x: x[0])[1]

        # 用形态学操作改进掩码质量
        mask = (mask * 255).astype(np.uint8)

        # 应用形态学操作清理掩码
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

        # 应用高斯模糊平滑边缘
        mask = cv2.GaussianBlur(mask, (3, 3), 0)

        # 模糊后重新阈值化
        _, mask = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)

        # 用更高精度提取轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        if not contours:
            raise ValueError("未检测到人体轮廓")

        # 获取最大的轮廓
        max_contour = max(contours, key=cv2.contourArea)

        # 简化轮廓以减少噪声同时保持形状
        epsilon = 0.002 * cv2.arcLength(max_contour, True)
        max_contour = cv2.approxPolyDP(max_contour, epsilon, True)

        # 改进的归一化，使用基于中心的缩放
        moments = cv2.moments(max_contour)
        if moments['m00'] != 0:
            # 计算质心
            cx = int(moments['m10'] / moments['m00'])
            cy = int(moments['m01'] / moments['m00'])

            # 将轮廓平移到原点（中心在0,0）
            max_contour = max_contour - [cx, cy]

            # 基于从中心的最大距离计算尺度
            distances = np.sqrt(np.sum(max_contour.reshape(-1, 2)**2, axis=1))
            max_distance = np.max(distances)

            if max_distance > 0:
                scale = 50.0 / max_distance  # 缩放到适合100x100并留边距
                max_contour = (max_contour * scale).astype(np.float32)
        else:
            # 回退到边界框归一化
            x, y, w, h = cv2.boundingRect(max_contour)
            max_contour = max_contour - [x + w//2, y + h//2]  # 居中
            scale = 50.0 / max(w, h) if max(w, h) > 0 else 1.0
            max_contour = (max_contour * scale).astype(np.float32)

        return max_contour

    def detect_animal_in_image(self, image_path):
        """使用label museum中的标注文件获取动物类型和分割掩码"""
        try:
            # 获取图像文件名（不带路径和扩展名）
            image_filename = os.path.basename(image_path)
            image_name = os.path.splitext(image_filename)[0]

            # 查找对应的JSON标注文件
            label_museum_dir = Path('/Users/<USER>/Desktop/label museum')
            json_file_path = label_museum_dir / f"{image_name}.json"

            # 如果没有找到对应的标注文件，返回默认结果
            if not json_file_path.exists():
                print(f"未找到标注文件：{json_file_path}")
                return "未标注动物", None

            # 读取图像获取尺寸
            orig_img = cv2.imread(image_path)
            if orig_img is None:
                return "无法读取图像", None

            orig_h, orig_w = orig_img.shape[:2]

            # 读取JSON标注文件
            with open(json_file_path, 'r') as f:
                data = json.load(f)

            # 提取标签和多边形点
            shapes = data.get('shapes', [])
            if not shapes:
                return "未标注动物", None

            # 使用第一个标签作为动物名称
            animal_type = shapes[0].get('label', "未知动物")

            # 创建掩码图像
            mask = np.zeros((orig_h, orig_w), dtype=np.uint8)

            # 处理所有相同标签的多边形，将它们合并到一个掩码中
            for shape in shapes:
                if shape.get('label') == animal_type and shape.get('shape_type') == 'polygon':
                    # 提取多边形点坐标
                    points = np.array(shape.get('points', []), dtype=np.int32)
                    if len(points) < 3:  # 至少需要3个点才能形成多边形
                        continue

                    # 在掩码上绘制多边形
                    cv2.fillPoly(mask, [points], 255)

            return animal_type, mask

        except Exception as e:
            print(f"标注文件处理出错: {e}")
            return "处理错误", None

    def create_overlay_image(self, image_path, contour, output_path):
        """创建带轮廓覆盖的图像"""
        try:
            # 读取原图像
            img = cv2.imread(str(image_path))
            if img is None:
                return False
                
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            overlay_img = img.copy()
            img_h, img_w = img.shape[:2]

            # 调整轮廓尺寸
            resize_factor = min(img_h, img_w) / 100.0
            resized_contour = (contour * resize_factor).astype(np.int32)

            # 居中轮廓
            x, y, w, h = cv2.boundingRect(resized_contour)
            center_offset_x = (img_w - w) // 2 - x
            center_offset_y = (img_h - h) // 2 - y
            centered_contour = resized_contour + [center_offset_x, center_offset_y]

            # 绘制轮廓
            cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)
            
            # 创建半透明填充
            mask = np.zeros(overlay_img.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [centered_contour], 255)
            
            overlay = np.zeros_like(overlay_img)
            overlay[mask == 255] = [0, 255, 0]
            
            alpha = 0.3
            cv2.addWeighted(overlay, alpha, overlay_img, 1 - alpha, 0, overlay_img)

            # 保存图像
            plt.imsave(str(output_path), overlay_img)
            return True
            
        except Exception as e:
            print(f"创建覆盖图像失败: {e}")
            return False

    def crop_to_square(self, image):
        """将图像裁剪为1:1比例"""
        h, w = image.shape[:2]
        if w > h:
            # 宽大于高，裁剪宽度
            crop_size = h
            start_x = (w - crop_size) // 2
            cropped_img = image[:, start_x:start_x+crop_size]
        else:
            # 高大于宽，裁剪高度
            crop_size = w
            start_y = (h - crop_size) // 2
            cropped_img = image[start_y:start_y+crop_size, :]
        
        return cropped_img 